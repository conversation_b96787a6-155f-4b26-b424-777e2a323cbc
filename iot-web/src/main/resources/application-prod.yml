spring:
  application:
    name: micro-web

  config:
    import: nacos:micro-web-prod.yaml?group=DEFAULT_GROUP&namespace=public&refresh=true

  cloud:
    nacos:
      config:
        server-addr: 10.0.0.2:8848
        username: nacos
        password: 4!IkS3}s1z$Ymo%_
        namespace: public
        file-extension: yaml
        group: DEFAULT_GROUP

      discovery:
        server-addr: 10.0.0.2:8848
        username: nacos
        password: 4!IkS3}s1z$Ymo%_
        namespace: public
logging:
  level:
    com.alibaba.nacos: WARN
    com.alibaba.cloud.nacos: WARN
