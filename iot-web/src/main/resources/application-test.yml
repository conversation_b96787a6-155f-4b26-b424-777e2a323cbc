spring:
  application:
    name: iot-web

  config:
    import: nacos:iot-web-test.yaml?group=DEFAULT_GROUP&namespace=public&refresh=true

  cloud:
    nacos:
      config:
        server-addr: http://124.222.23.237:8848
        username: nacos
        password: 123456Aa!
        namespace: public
        file-extension: yaml
        group: DEFAULT_GROUP

      discovery:
        server-addr: http://124.222.23.237:8848
        username: nacos
        password: 123456Aa!
        namespace: public

#logging:
#  level:
#    com.alibaba.nacos: WARN
#    com.alibaba.cloud.nacos: WARN


logging:
  level:
    org.apache.ibatis: DEBUG
