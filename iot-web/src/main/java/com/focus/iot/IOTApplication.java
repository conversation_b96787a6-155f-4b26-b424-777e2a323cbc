package com.focus.iot;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.focus"}) // ✅ 不再排除 DataSource 自动配置
@MapperScan("com.focus.**.mapper") // ✅ 保留
@EnableFeignClients(basePackages = "com.focus")
@EnableAsync
@EnableScheduling
public class IOTApplication {
    public static void main(String... args) {
        SpringApplication.run(IOTApplication.class, args);
    }
}

