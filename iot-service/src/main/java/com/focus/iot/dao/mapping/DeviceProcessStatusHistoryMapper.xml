<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceProcessStatusHistoryMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceProcessStatusHistory">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="processId" column="process_id" jdbcType="VARCHAR"/>
            <result property="processType" column="process_type" jdbcType="INTEGER"/>
            <result property="deviceId" column="device_id" jdbcType="VARCHAR"/>
            <result property="gpuIndex" column="gpu_index" jdbcType="INTEGER"/>
            <result property="processStatus" column="process_status" jdbcType="INTEGER"/>
            <result property="taskStatus" column="task_status" jdbcType="INTEGER"/>
            <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
            <result property="heartbeatTime" column="heartbeat_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,process_id,process_type,
        device_id,gpu_index,process_status,task_status,task_id,heartbeat_time,
        create_time
    </sql>

    <select id="selectDataByCursor" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_device_process_status_history
        WHERE create_time &gt; #{startTime} AND create_time &lt; #{endTime}
        ORDER BY id
    </select>
</mapper>
