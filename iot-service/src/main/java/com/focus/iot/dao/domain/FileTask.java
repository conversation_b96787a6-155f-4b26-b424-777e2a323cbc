package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务表
 * @TableName t_file_task
 */
@TableName(value ="t_file_task")
@Data
public class FileTask {
    /**
     * 任务主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务编号
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 远程文件地址
     */
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * 本地要保存的地址
     */
    @TableField(value = "target_path")
    private String targetPath;

    /**
     * 文件编号
     */
    @TableField(value = "file_code")
    private String fileCode;

    /**
     * 任务处理结果文件地址
     */
    @TableField(value = "result_file_path")
    private String resultFilePath;

    /**
     * 失败尝试次数
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 下载任务的并发线程数
     */
    @TableField(value = "thread_count")
    private Integer threadCount;

    /**
     * 任务状态(0-初始化，1-执行中，2-成功，3-失败)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "createt_time")
    private LocalDateTime createtTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 删除状态(0-有效,1删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;
}