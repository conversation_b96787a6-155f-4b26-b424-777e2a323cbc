package com.focus.iot.dao.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.DeviceStatusHistory;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 针对表【t_device_status_history(设备状态历史记录表：记录设备整体资源使用情况)】的数据库操作Service
 * @createDate 2025-08-25 16:44:05
 */
public interface IDeviceStatusHistoryService extends IService<DeviceStatusHistory> {
    void saveDeviceStatusInfo(JSONObject payloadJson, BigDecimal gpuUtilAvg);

    long clearDeviceStatusHistory(LocalDateTime endTime);

    /**
     * 上传GPU数据到OSS
     */
    long uploadDeviceStatusDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String region);
}
