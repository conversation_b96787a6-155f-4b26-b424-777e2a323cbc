package com.focus.iot.dao.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.DeviceStatus;

import java.math.BigDecimal;

/**
* <AUTHOR>
* @description 针对表【t_device_status(设备当前运行状态表：记录设备最近一次上报的实时状态)】的数据库操作Service
* @createDate 2025-08-25 16:44:05
*/
public interface IDeviceStatusService extends IService<DeviceStatus> {
    void updateDeviceStatusInfo(JSONObject payloadJson, BigDecimal gpuUtilAvg, BigDecimal gpuMemAvg);
}
