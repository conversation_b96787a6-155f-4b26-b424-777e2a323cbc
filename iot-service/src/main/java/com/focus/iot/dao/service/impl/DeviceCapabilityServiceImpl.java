package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceCapability;
import com.focus.iot.dao.mapper.DeviceCapabilityMapper;
import com.focus.iot.dao.service.IDeviceCapabilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_device_capability(设备能力表：记录每个设备支持的 AI 能力类型)】的数据库操作Service实现
 * @createDate 2025-08-25 16:44:05
 */
@Service
public class DeviceCapabilityServiceImpl extends ServiceImpl<DeviceCapabilityMapper, DeviceCapability>
        implements IDeviceCapabilityService {

    @Autowired
    private DeviceCapabilityMapper deviceCapabilityMapper;

    @Override
    public void saveDeviceCapability(JSONObject json) {
        // 如果是子进程，则保留能力信息
        String deviceId = json.getStr(HeartBeatConstant.DEVICE_ID);
        String processId = json.getStr(HeartBeatConstant.PROCESS_ID);
        JSONArray capabilities = json.getJSONArray(HeartBeatConstant.CAPABILITIES);
        List<DeviceCapability> deviceCapabilityList = new ArrayList<>();

        for (int i = 0; i < capabilities.size(); i++) {
            String capability = capabilities.getStr(i);
            DeviceCapability deviceCapability = DeviceCapability.builder()
                    .deviceId(deviceId)
                    .processId(processId)
                    .capabilityType(capability)
                    .build();
            deviceCapabilityList.add(deviceCapability);
        }

        if (!capabilities.isEmpty()) {
            deviceCapabilityMapper.saveOrUpdateDeviceCapabilityBatch(deviceCapabilityList);
        }

    }
}




