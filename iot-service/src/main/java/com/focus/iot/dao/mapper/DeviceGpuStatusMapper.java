package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.DeviceGpuStatus;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_device_gpu_status(GPU 实时状态表)】的数据库操作Mapper
* @createDate 2025-08-25 16:44:05
* @Entity generator.domain.DeviceGpuStatus
*/
public interface DeviceGpuStatusMapper extends BaseMapper<DeviceGpuStatus> {
    void saveOrUpdateDeviceGpuStatus(DeviceGpuStatus entity);

    void saveOrUpdateDeviceGpuStatusBatch(List<DeviceGpuStatus> entity);
}




