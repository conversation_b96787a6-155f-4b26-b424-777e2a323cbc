package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.DeviceCapability;
import com.focus.iot.dao.domain.DeviceGpuStatus;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_device_capability(设备能力表：记录每个设备支持的 AI 能力类型)】的数据库操作Mapper
* @createDate 2025-08-25 16:44:05
* @Entity generator.domain.DeviceCapability
*/
public interface DeviceCapabilityMapper extends BaseMapper<DeviceCapability> {

    void saveOrUpdateDeviceCapabilityBatch(List<DeviceCapability> entity);
}




