<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.HardwareUsageMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.HardwareUsage">
            <id property="id" column="id" />
            <result property="hostName" column="host_name" />
            <result property="port" column="port" />
            <result property="cpuUsage" column="cpu_usage" />
            <result property="memoryTotal" column="memory_total" />
            <result property="memoryUsed" column="memory_used" />
            <result property="memoryUsage" column="memory_usage" />
            <result property="diskUsed" column="disk_used" />
            <result property="diskTotal" column="disk_total" />
            <result property="diskUsage" column="disk_usage" />
            <result property="gpuMemoryUsed" column="gpu_memory_used" />
            <result property="gpuMemoryTotal" column="gpu_memory_total" />
            <result property="gpuMemoryUsage" column="gpu_memory_usage" />
            <result property="gpuLoad" column="gpu_load" />
            <result property="createTime" column="create_time" />
    </resultMap>


    <!-- VO映射 -->
    <resultMap id="HardwareUsageVOMap" type="com.focus.iot.vo.HardwareUsageVO">
        <result property="id" column="id" />
        <result property="hostName" column="host_name" />
        <result property="port" column="port" />
        <result property="cpuUsage" column="cpu_usage" />
        <result property="memoryTotal" column="memory_total" />
        <result property="memoryUsed" column="memory_used" />
        <result property="memoryUsage" column="memory_usage" />
        <result property="diskUsed" column="disk_used" />
        <result property="diskTotal" column="disk_total" />
        <result property="diskUsage" column="disk_usage" />
        <result property="gpuMemoryUsed" column="gpu_memory_used" />
        <result property="gpuMemoryTotal" column="gpu_memory_total" />
        <result property="gpuMemoryUsage" column="gpu_memory_usage" />
        <result property="gpuLoad" column="gpu_load" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,host_name,port,cpu_usage,memory_total,memory_used,
        memory_usage,disk_used,disk_total,disk_usage,gpu_memory_used,
        gpu_memory_total,gpu_memory_usage,gpu_load,create_time
    </sql>




    <select id="selectLatestByOptionalIp" resultMap="HardwareUsageVOMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM (
        SELECT <include refid="Base_Column_List"/>,
        ROW_NUMBER() OVER (PARTITION BY host_name, port ORDER BY create_time DESC) AS rn
        FROM t_hardware_usage
        <where>
            <if test="hostName != null and hostName.trim() != '' and hostName != 'null'">
                host_name = #{hostName}
            </if>
        </where>
        ) t
        WHERE t.rn = 1
    </select>

</mapper>
