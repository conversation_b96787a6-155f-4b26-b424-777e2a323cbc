package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.FileProcessLog;
import com.focus.iot.dao.mapper.FileProcessLogMapper;
import com.focus.iot.dao.service.IFileProcessLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_file_process_log(文件处理流程日志表)】的数据库操作Service实现
* @createDate 2025-06-27 15:41:39
*/
@Service
public class FileProcessLogServiceImpl extends ServiceImpl<FileProcessLogMapper, FileProcessLog>
    implements IFileProcessLogService {

}




