package com.focus.iot.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.HardwareUsage;
import com.focus.iot.vo.HardwareUsageVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_hardware_usage】的数据库操作Service
 * @createDate 2025-04-27 17:54:42
 */
public interface IHardwareUsageService extends IService<HardwareUsage> {
    List<HardwareUsageVO> selectLatestByOptionalIp(String hostName);
}
