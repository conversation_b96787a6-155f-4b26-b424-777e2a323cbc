package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 算力服务器设备报警信息表
 * @TableName t_device_alarm
 */
@TableName(value ="t_device_alarm")
@Data
public class DeviceAlarm {
    /**
     * 主键 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备唯一标识（如服务器编号）
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * 报警类型（如 CPU 异常、内存不足、GPU 离线等）
     */
    @TableField(value = "alarm_type")
    private String alarmType;

    /**
     * 报警级别：1-低，2-中，3-高
     */
    @TableField(value = "alarm_level")
    private Integer alarmLevel;

    /**
     * 报警详细信息描述
     */
    @TableField(value = "alarm_message")
    private String alarmMessage;

    /**
     * 报警状态：0-未处理，1-已确认，2-已处理
     */
    @TableField(value = "alarm_status")
    private Integer alarmStatus;

    /**
     * 报警发生时间
     */
    @TableField(value = "alarm_time")
    private LocalDateTime alarmTime;

    /**
     * 报警确认时间
     */
    @TableField(value = "confirm_time")
    private LocalDateTime confirmTime;

    /**
     * 报警解决时间
     */
    @TableField(value = "resolved_time")
    private LocalDateTime resolvedTime;

    /**
     * 报警记录创建人或来源（如系统、管理员）
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 记录创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}