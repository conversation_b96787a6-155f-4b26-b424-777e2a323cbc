package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * GPU 状态历史记录表：记录设备各 GPU 的状态数据快照
 *
 * @TableName t_device_gpu_status_history
 */
@TableName(value = "t_device_gpu_status_history")
@Data
@Builder
public class DeviceGpuStatusHistory {
    /**
     * 主键 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备唯一 ID
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * GPU 索引号（从 0 开始）
     */
    @TableField(value = "gpu_index")
    private Integer gpuIndex;

    /**
     * GPU 利用率（百分比）
     */
    @TableField(value = "utilization")
    private BigDecimal utilization;

    /**
     * 已使用显存（单位：MB）
     */
    @TableField(value = "memory_used")
    private Integer memoryUsed;

    /**
     * GPU 温度（单位：℃）
     */
    @TableField(value = "temperature")
    private BigDecimal temperature;

    /**
     * GPU 当前功耗（单位：W）
     */
    @TableField(value = "power_draw")
    private BigDecimal powerDraw;

    /**
     * 心跳上报时间
     */
    @TableField(value = "heartbeat_time")
    private LocalDateTime heartbeatTime;

    /**
     * 记录创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
}