package com.focus.iot.dao.service.impl;

import com.focus.iot.dao.service.ITableManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TableManagerServiceImpl implements ITableManagerService {

    private final JdbcTemplate jdbcTemplate;

    public boolean tableExists(String tableName) {
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
        return count != null && count > 0;
    }

    public void createTableIfNotExists(String tableName, String templateTable) {
        if (!tableExists(tableName)) {
            String sql = String.format("CREATE TABLE `%s` LIKE `%s`", tableName, templateTable);
            jdbcTemplate.execute(sql);
        }
    }

    /*tableManager.createTableIfNotExists("t_file_base_202412", "t_file_base_template");*/
}
