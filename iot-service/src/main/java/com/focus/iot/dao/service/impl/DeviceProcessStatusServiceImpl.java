package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceProcessStatus;
import com.focus.iot.dao.mapper.DeviceProcessStatusMapper;
import com.focus.iot.dao.service.IDeviceProcessStatusService;
import com.focus.iot.util.LocalDateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/9/1
 * @Desc
 **/
@Service
public class DeviceProcessStatusServiceImpl extends ServiceImpl<DeviceProcessStatusMapper, DeviceProcessStatus>
        implements IDeviceProcessStatusService {

    @Autowired
    private DeviceProcessStatusMapper deviceProcessStatusMapper;

    @Override
    public void updateProcessStatusInfo(JSONObject payloadJson) {
        // 1、获取gpu数组（JSONArray类型）
        JSONArray processArray = payloadJson.getJSONArray(HeartBeatConstant.PROCESS);
        List<DeviceProcessStatus> processStatusList = new ArrayList<>();
        if(processArray == null || processArray.isEmpty()){
            return;
        }

        // 2、遍历
        for (int i = 0; i < processArray.size(); i++) {
            JSONObject processJson = processArray.getJSONObject(i);
            // 3、实时更新GPU数据
            DeviceProcessStatus processStatus = DeviceProcessStatus.builder()
                    .processId(processJson.getStr(HeartBeatConstant.PROCESS_ID))
                    .processType(processJson.getInt(HeartBeatConstant.PROCESS_TYPE))
                    .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                    .gpuIndex(processJson.getInt(HeartBeatConstant.GPU_INDEX))
                    .processStatus(processJson.getInt(HeartBeatConstant.PROCESS_STATUS))
                    .taskStatus(processJson.getInt(HeartBeatConstant.TASK_STATUS))
                    .taskId(processJson.getStr(HeartBeatConstant.TASK_ID))
                    .lastHeartbeatTime(LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA))
                    .build();
            processStatusList.add(processStatus);
        }
        deviceProcessStatusMapper.saveOrUpdateDeviceProcessStatusBatch(processStatusList);
    }
}
