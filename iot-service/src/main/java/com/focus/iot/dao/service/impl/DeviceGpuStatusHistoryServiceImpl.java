package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceGpuStatusHistory;
import com.focus.iot.dao.mapper.DeviceGpuStatusHistoryMapper;
import com.focus.iot.dao.service.IDeviceGpuStatusHistoryService;
import com.focus.iot.enums.UploadFileTypeEnum;
import com.focus.iot.util.LocalDateTimeUtil;
import com.focus.iot.util.UploadOssUtil;
import com.focus.iot.vo.GpuInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_device_gpu_status_history(GPU 状态历史记录表：记录设备各 GPU 的状态数据快照)】的数据库操作Service实现
 * @createDate 2025-08-25 16:44:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceGpuStatusHistoryServiceImpl extends ServiceImpl<DeviceGpuStatusHistoryMapper, DeviceGpuStatusHistory>
        implements IDeviceGpuStatusHistoryService {

    @Autowired
    private UploadOssUtil uploadOssUtil;

    @Override
    public List<GpuInfoVO> saveGpuStatusInfo(JSONObject payloadJson) {
        // 1、获取gpu数组（JSONArray类型）
        JSONArray gpuArray = payloadJson.getJSONArray(HeartBeatConstant.GPU);
        List<DeviceGpuStatusHistory> gpuStatusHistoryList = new ArrayList<>();
        List<GpuInfoVO> gpuInfoVOList = new ArrayList<>();

        // 2、遍历
        for (int i = 0; i < gpuArray.size(); i++) {
            JSONObject gpuJson = gpuArray.getJSONObject(i);
            // GPU心跳信息
            DeviceGpuStatusHistory gpuStatusHistory = DeviceGpuStatusHistory.builder()
                    .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                    .gpuIndex(gpuJson.getInt(HeartBeatConstant.GPU_INDEX))
                    .memoryUsed(gpuJson.getInt(HeartBeatConstant.MEMORY_USED))
                    .temperature(BigDecimal.valueOf(gpuJson.getFloat(HeartBeatConstant.TEMPERATURE)))
                    .utilization(BigDecimal.valueOf(gpuJson.getFloat(HeartBeatConstant.UTILIZATION)))
                    .powerDraw(BigDecimal.valueOf(gpuJson.getFloat(HeartBeatConstant.POWER_DRAW)))
                    .heartbeatTime(LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA))
                    .build();
            gpuStatusHistoryList.add(gpuStatusHistory);

            // 设备需要统计的信息
            GpuInfoVO gpuInfoVO = GpuInfoVO.builder()
                    .utilization(gpuStatusHistory.getUtilization())
                    .memoryTotal(gpuJson.getInt(HeartBeatConstant.MEMORY_TOTAL))
                    .memoryUsed(gpuStatusHistory.getMemoryUsed()).build();
            gpuInfoVOList.add(gpuInfoVO);
        }

        // 3、保存到GPU历史记录表
        this.saveBatch(gpuStatusHistoryList);
        // 返回额外信息
        return gpuInfoVOList;
    }

    @Override
    public long clearGpuStatusHistory(LocalDateTime endTime) {
        long removeNum = this.count(new QueryWrapper<DeviceGpuStatusHistory>().lt("create_time", endTime));
        this.remove(new QueryWrapper<DeviceGpuStatusHistory>().lt("create_time", endTime));
        return removeNum;
    }

    @Override
    public long uploadGpuStatusDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String region) {

        // 生成唯一CSV文件名（避免重复）
        String yesterdayStr = LocalDateTimeUtil.getYesterdayDate(LocalDateTimeUtil.YYYYMMDD);
        String fileName = String.format("heartbeat/gpu/%s/gpu_status_data_%s.csv", region, yesterdayStr);
        return uploadOssUtil.uploadDataToOSS(startTime, endTime, fileName, UploadFileTypeEnum.GPU_STATUS_HISTORY.getType());
    }
}








