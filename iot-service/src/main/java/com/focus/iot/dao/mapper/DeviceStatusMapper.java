package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.DeviceStatus;

/**
* <AUTHOR>
* @description 针对表【t_device_status(设备当前运行状态表：记录设备最近一次上报的实时状态)】的数据库操作Mapper
* @createDate 2025-08-25 16:44:05
* @Entity generator.domain.DeviceStatus
*/
public interface DeviceStatusMapper extends BaseMapper<DeviceStatus> {
    void saveOrUpdateDeviceStatus(DeviceStatus deviceStatus);
}




