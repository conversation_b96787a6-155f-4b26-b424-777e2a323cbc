package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件下载任务表
 * @TableName t_file_download_tasks
 */
@TableName(value ="t_file_download_tasks")
@Data
public class FileDownloadTasks {
    /**
     * 任务ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联文件编号
     */
    @TableField(value = "file_code")
    private String fileCode;

    /**
     * 重试次数
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 计划执行时间
     */
    @TableField(value = "scheduled_at")
    private LocalDateTime scheduledAt;

    /**
     * 任务开始时间
     */
    @TableField(value = "started_at")
    private LocalDateTime startedAt;

    /**
     * 任务完成时间
     */
    @TableField(value = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 任务状态：pending/running/success/failed
     */
    @TableField(value = "status")
    private String status;

    /**
     * 失败错误信息
     */
    @TableField(value = "error_msg")
    private String errorMsg;

    /**
     * 请求时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 状态(0-有效,1删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;
}