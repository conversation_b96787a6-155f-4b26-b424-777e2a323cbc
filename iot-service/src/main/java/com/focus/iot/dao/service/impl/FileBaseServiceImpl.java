package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.focus.iot.dao.domain.FileBase;
import com.focus.iot.dao.mapper.FileBaseMapper;
import com.focus.iot.dao.service.IFileBaseService;
import com.focus.iot.dao.service.ITableManagerService;
import com.focus.iot.dto.FileBasePageQuery;
import com.focus.iot.util.FileTableRouterUtil;
import com.focus.iot.util.TableNameHolder;
import com.focus.framework.dto.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileBaseServiceImpl implements IFileBaseService {

    private final FileBaseMapper fileBaseMapper;
    private final ITableManagerService tableManagerService;


    /**
     * 1. 确保分表存在，如果不存在则创建
     * 2. 计算应插入的分表后缀
     * 3. 设置线程变量，影响 MyBatis 表名路由
     * 4. 正常调用 Mapper 插入
     * 5. 清理线程变量，避免污染
     */
    public void saveFile(FileBase file) {

        // 2. 计算应插入的分表后缀
        String suffix = FileTableRouterUtil.getInsertTableSuffix(LocalDateTime.now()); // 如 "202506"
        tableManagerService.createTableIfNotExists("t_file_base" + "_" + suffix, "t_file_base");
        try {
            // 3. 设置线程变量，影响 MyBatis 表名路由
            TableNameHolder.set(suffix);

            // 4. 正常调用 Mapper 插入
            fileBaseMapper.insert(file);

        } finally {
            // 5. 清理线程变量，避免污染
            TableNameHolder.clear();
        }
    }


    /**
     * 修改文件状态和错误信息
     *
     * @param fileCode   逻辑主键（file_code）
     * @param status     下载状态，例如 "success" 或 "failed"
     * @param errorMsg   错误信息，可为空
     * @param createTime 创建时间（用于路由分表）
     */
    public void updateStatus(String fileCode, String status, String errorMsg, LocalDateTime createTime) {
        String suffix = FileTableRouterUtil.getInsertTableSuffix(createTime); // 分表路由
        try {
            TableNameHolder.set(suffix);

            FileBase update = new FileBase();
            update.setDownloadStatus(status);
            update.setErrorMsg(errorMsg);
            update.setUpdateTime(LocalDateTime.now());

            LambdaQueryWrapper<FileBase> wrapper = Wrappers.<FileBase>lambdaQuery()
                    .eq(FileBase::getFileCode, fileCode);

            fileBaseMapper.update(update, wrapper);

        } finally {
            TableNameHolder.clear();
        }
    }

    /**
     * 分布式分页查询文件信息
     * 1. 获取查询参数，计算分页起始位置
     * 2. 获取所有相关分表后缀
     * 3. 并行查询每个分表，合并结果
     * 4. 对合并结果进行排序和分页处理
     */
    @Override
    public PageResult<FileBase> distributedPageQuery(FileBasePageQuery query) {
        int page = query.getPage();
        int size = query.getSize();
        int fetchLimit = page * size;

        List<String> suffixList = FileTableRouterUtil.listTableSuffix(query.getStartTime(), query.getEndTime());

        List<FileBase> merged = suffixList.parallelStream()
                .flatMap(suffix -> {
                    try {
                        TableNameHolder.set(suffix);
                        LambdaQueryWrapper<FileBase> wrapper = Wrappers.lambdaQuery();
                        wrapper.ge(FileBase::getCreateTime, query.getStartTime());
                        wrapper.le(FileBase::getCreateTime, query.getEndTime());

                        if (query.getFileCode() != null && !query.getFileCode().isBlank()) {
                            wrapper.eq(FileBase::getFileCode, query.getFileCode());
                        }
                        if (query.getFileName() != null && !query.getFileName().isBlank()) {
                            wrapper.like(FileBase::getFileName, query.getFileName());
                        }
                        if (query.getFileType() != null && !query.getFileType().isBlank()) {
                            wrapper.eq(FileBase::getFileType, query.getFileType());
                        }

                        wrapper.orderByDesc(FileBase::getCreateTime);

                        Page<FileBase> pageInfo = new Page<>(1, fetchLimit);
                        return fileBaseMapper.selectPage(pageInfo, wrapper).getRecords().stream();

                    } catch (Exception e) {
                        log.error("查询分表 [{}] 失败", suffix, e);
                        return Stream.empty();
                    } finally {
                        TableNameHolder.clear();
                    }
                })
                .sorted(Comparator.comparing(FileBase::getCreateTime).reversed())
                .toList();

        int total = merged.size();
        int fromIndex = (page - 1) * size;
        int toIndex = Math.min(fromIndex + size, total);

        List<FileBase> pageList = (fromIndex < total) ? merged.subList(fromIndex, toIndex) : List.of();

        return new PageResult<>(total, pageList);
    }
}
