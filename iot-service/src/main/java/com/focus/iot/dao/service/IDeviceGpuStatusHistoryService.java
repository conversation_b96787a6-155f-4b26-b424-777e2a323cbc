package com.focus.iot.dao.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.DeviceGpuStatusHistory;
import com.focus.iot.vo.GpuInfoVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_device_gpu_status_history(GPU 状态历史记录表：记录设备各 GPU 的状态数据快照)】的数据库操作Service
 * @createDate 2025-08-25 16:44:05
 */
public interface IDeviceGpuStatusHistoryService extends IService<DeviceGpuStatusHistory> {

    List<GpuInfoVO> saveGpuStatusInfo(JSONObject payloadJson);

    long clearGpuStatusHistory(LocalDateTime endTime);

    /**
     * 上传GPU数据到OSS
     * */
    long uploadGpuStatusDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String region);

}
