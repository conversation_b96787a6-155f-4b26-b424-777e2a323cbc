package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.FileDownloadTasks;
import com.focus.iot.dao.mapper.FileDownloadTasksMapper;
import com.focus.iot.dao.service.IFileDownloadTasksService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_file_download_tasks(文件下载任务表)】的数据库操作Service实现
* @createDate 2025-06-27 15:41:39
*/
@Service
public class FileDownloadTasksServiceImpl extends ServiceImpl<FileDownloadTasksMapper, FileDownloadTasks>
    implements IFileDownloadTasksService {

}




