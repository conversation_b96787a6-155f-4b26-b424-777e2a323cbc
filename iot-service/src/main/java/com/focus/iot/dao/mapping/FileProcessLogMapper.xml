<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.FileProcessLogMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.FileProcessLog">
            <id property="id" column="id" />
            <result property="fileCode" column="file_code" />
            <result property="step" column="step" />
            <result property="status" column="status" />
            <result property="message" column="message" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,file_code,step,status,message,create_time
    </sql>
</mapper>
