package com.focus.iot.dao.service;

import com.focus.iot.dao.domain.FileBase;
import com.focus.iot.dto.FileBasePageQuery;
import com.focus.framework.dto.PageResult;

import java.time.LocalDateTime;

public interface IFileBaseService {

    /**
     * 1. 确保分表存在，如果不存在则创建
     * 2. 计算应插入的分表后缀
     * 3. 设置线程变量，影响 MyBatis 表名路由
     * 4. 正常调用 Mapper 插入
     * 5. 清理线程变量，避免污染
     */
    void saveFile(FileBase file);

    /**
     * 修改文件状态和错误信息
     *
     * @param fileCode   逻辑主键（file_code）
     * @param status     下载状态，例如 "success" 或 "failed"
     * @param errorMsg   错误信息，可为空
     * @param createTime 创建时间（用于路由分表）
     */
    void updateStatus(String fileCode, String status, String errorMsg, LocalDateTime createTime);

    /**
     * 分布式分页查询文件信息
     * 1. 获取查询参数，计算分页起始位置
     * 2. 获取所有相关分表后缀
     * 3. 并行查询每个分表，合并结果
     * 4. 对合并结果进行排序和分页处理
     */
    PageResult<FileBase> distributedPageQuery(FileBasePageQuery query);

}
