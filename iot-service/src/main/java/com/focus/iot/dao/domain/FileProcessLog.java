package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理流程日志表
 * @TableName t_file_process_log
 */
@TableName(value ="t_file_process_log")
@Data
public class FileProcessLog {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文件编号
     */
    @TableField(value = "file_code")
    private String fileCode;

    /**
     * 处理步骤，如 0-receive1-download 2-upload
     */
    @TableField(value = "step")
    private Integer step;

    /**
     * 处理状态：0-init,1-success,2-failed/warning
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 日志信息，如错误详情
     */
    @TableField(value = "message")
    private String message;

    /**
     * 处理时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
}