package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备当前运行状态表：记录设备最近一次上报的实时状态
 * @TableName t_device_status
 */
@TableName(value ="t_device_status")
@Data
@Builder
public class DeviceStatus {
    /**
     * 设备唯一 ID
     */
    @TableId(value = "device_id")
    private String deviceId;

    /**
     * 运行状态：0-在线，1-离线，2-异常
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * CPU 使用率（百分比）
     */
    @TableField(value = "cpu_usage")
    private BigDecimal cpuUsage;

    /**
     * 内存使用率（百分比）
     */
    @TableField(value = "memory_usage")
    private BigDecimal memoryUsage;

    /**
     * 磁盘使用率（百分比）
     */
    @TableField(value = "disk_usage")
    private BigDecimal diskUsage;

    /**
     * GPU 平均利用率（百分比）
     */
    @TableField(value = "gpu_util_avg")
    private BigDecimal gpuUtilAvg;

    /**
     * GPU 平均显存占用率（百分比）
     */
    @TableField(value = "gpu_mem_avg")
    private BigDecimal gpuMemAvg;

    /**
     * 网络流入速率（单位：bps）
     */
    @TableField(value = "network_in")
    private Long networkIn;

    /**
     * 网络流出速率（单位：bps）
     */
    @TableField(value = "network_out")
    private Long networkOut;

    /**
     * 设备温度（单位：℃）
     */
    @TableField(value = "temperature")
    private BigDecimal temperature;

    /**
     * 最后心跳上报时间
     */
    @TableField(value = "last_heartbeat_time")
    private LocalDateTime lastHeartbeatTime;

    /**
     * 记录更新时间（自动更新）
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}