<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.FileTaskMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.FileTask">
        <id property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="fileUrl" column="file_url"/>
        <result property="targetPath" column="target_path"/>
        <result property="fileCode" column="file_code"/>
        <result property="resultFilePath" column="result_file_path"/>
        <result property="retryCount" column="retry_count"/>
        <result property="threadCount" column="thread_count"/>
        <result property="status" column="status"/>
        <result property="createtTime" column="createt_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,task_id,file_url,target_path,file_code,result_file_path,
        retry_count,thread_count,status,createt_time,update_time,
        deleted
    </sql>
</mapper>
