package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 * @TableName t_hardware_usage
 */
@TableName(value ="t_hardware_usage")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HardwareUsage {
    /**
     * 监控主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主机名/IP 地址主机
     */
    @TableField(value = "host_name")
    private String hostName;

    /**
     * 端口号
     */
    @TableField(value = "port")
    private Integer port;

    /**
     * cpu使用率
     */
    @TableField(value = "cpu_usage")
    private BigDecimal cpuUsage;

    /**
     * 总内存(GB)
     */
    @TableField(value = "memory_total")
    private BigDecimal memoryTotal;

    /**
     * 已用内存(GB)
     */
    @TableField(value = "memory_used")
    private BigDecimal memoryUsed;

    /**
     * 内存使用率
     */
    @TableField(value = "memory_usage")
    private BigDecimal memoryUsage;

    /**
     * 磁盘已用(G)
     */
    @TableField(value = "disk_used")
    private BigDecimal diskUsed;

    /**
     * 磁盘总量(G)
     */
    @TableField(value = "disk_total")
    private BigDecimal diskTotal;

    /**
     * 磁盘使用率
     */
    @TableField(value = "disk_usage")
    private BigDecimal diskUsage;

    /**
     * GPU 显存已用 (MB)
     */
    @TableField(value = "gpu_memory_used")
    private BigDecimal gpuMemoryUsed;

    /**
     * GPU 显存总量 (MB)
     */
    @TableField(value = "gpu_memory_total")
    private BigDecimal gpuMemoryTotal;

    /**
     * GPU 显存使用率
     */
    @TableField(value = "gpu_memory_usage")
    private BigDecimal gpuMemoryUsage;

    /**
     * GPU 算力使用率
     */
    @TableField(value = "gpu_load")
    private BigDecimal gpuLoad;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
}