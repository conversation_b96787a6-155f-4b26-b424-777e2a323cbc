package com.focus.iot.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.Config;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_config(配置表)】的数据库操作Service
 * @createDate 2023-08-20 10:53:55
 */
public interface IConfigService extends IService<Config> {
    /**
     * 查询分组配置
     *
     * @param gp
     * @param name
     * @return
     */
    List<Config> getGpList(String gp, String name);

    /**
     * 查询配置
     *
     * @param name
     * @return
     */
    Config getConfig(String name);

    /**
     * 查询配置
     *
     * @param gp
     * @param name
     * @return
     */
    Config getConfig(String gp, String name);

    /**
     * 查询文本内容配置
     *
     * @param name
     * @return
     */
    String getTextConfig(String name);

    /**
     * 查询文本内容配置
     *
     * @param gp
     * @param name
     * @return
     */
    String getTextConfig(String gp, String name);

    /**
     * 查询开关配置 1-开启 0-关闭
     *
     * @param name
     * @return
     */
    Boolean getSwitchConfig(String name);
}
