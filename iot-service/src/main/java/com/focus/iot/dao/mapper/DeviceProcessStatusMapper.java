package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.DeviceProcessStatus;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_device_process_status(设备进程信息实时状态表)】的数据库操作Mapper
* @createDate 2025-09-01 13:47:35
* @Entity com.focus.iot.dao.domain.TDeviceProcessStatus
*/
public interface DeviceProcessStatusMapper extends BaseMapper<DeviceProcessStatus>{
    void saveOrUpdateDeviceProcessStatusBatch(List<DeviceProcessStatus> entity);
}
