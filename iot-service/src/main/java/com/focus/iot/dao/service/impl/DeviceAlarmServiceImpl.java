package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.DeviceAlarm;
import com.focus.iot.dao.mapper.DeviceAlarmMapper;
import com.focus.iot.dao.service.IDeviceAlarmService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_device_alarm(算力服务器设备报警信息表)】的数据库操作Service实现
* @createDate 2025-08-25 16:44:05
*/
@Service
public class DeviceAlarmServiceImpl extends ServiceImpl<DeviceAlarmMapper, DeviceAlarm>
    implements IDeviceAlarmService {

}




