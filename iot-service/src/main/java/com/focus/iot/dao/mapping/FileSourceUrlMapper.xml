<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.FileSourceUrlMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.FileSourceUrl">
        <id property="id" column="id" />
        <result property="tableSuffix" column="table_suffix" />
        <result property="taskId" column="task_id" />
        <result property="fileId" column="file_id" />
        <result property="fileCode" column="file_code" />
        <result property="sourceUrl" column="source_url" />
        <result property="hash" column="hash" />
        <result property="sourceType" column="source_type" />
        <result property="isActive" column="is_active" />
        <result property="downloadStatus" column="download_status" />
        <result property="lastDownloadTime" column="last_download_time" />
        <result property="retryCount" column="retry_count" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="deleted" column="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,table_suffix,task_id,file_id,file_code,source_url,
        hash,source_type,is_active,download_status,last_download_time,
        retry_count,create_time,update_time,deleted
    </sql>
</mapper>
