package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.TaskLog;
import com.focus.iot.dao.mapper.TaskLogMapper;
import com.focus.iot.dao.service.ITaskLogService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_task_log(任务记录表)】的数据库操作Service实现
* @createDate 2025-07-10 17:58:11
*/
@Service
public class TaskLogServiceImpl extends ServiceImpl<TaskLogMapper, TaskLog>
    implements ITaskLogService {

}




