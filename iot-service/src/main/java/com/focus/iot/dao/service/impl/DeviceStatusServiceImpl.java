package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceStatus;
import com.focus.iot.dao.mapper.DeviceStatusMapper;
import com.focus.iot.dao.service.IDeviceStatusService;
import com.focus.iot.util.LocalDateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【t_device_status(设备当前运行状态表：记录设备最近一次上报的实时状态)】的数据库操作Service实现
 * @createDate 2025-08-25 16:44:05
 */
@Service
public class DeviceStatusServiceImpl extends ServiceImpl<DeviceStatusMapper, DeviceStatus>
        implements IDeviceStatusService {

    @Autowired
    private DeviceStatusMapper deviceStatusMapper;

    @Override
    public void updateDeviceStatusInfo(JSONObject payloadJson, BigDecimal gpuUtilAvg, BigDecimal gpuMemAvg) {
        DeviceStatus deviceStatus = DeviceStatus.builder()
                .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                .status(payloadJson.getInt(HeartBeatConstant.STATUS))
                .cpuUsage(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.CPU_USAGE)))
                .memoryUsage(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.MEMORY_USAGE)))
                .diskUsage(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.DISK_USAGE)))
                .gpuUtilAvg(gpuUtilAvg)
                .gpuMemAvg(gpuMemAvg)
                .networkIn(payloadJson.getLong(HeartBeatConstant.NETWORK_IN))
                .networkOut(payloadJson.getLong(HeartBeatConstant.NETWORK_OUT))
                .temperature(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.TEMPERATURE)))
                .lastHeartbeatTime(LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA))
                .build();
        deviceStatusMapper.saveOrUpdateDeviceStatus(deviceStatus);
    }
}




