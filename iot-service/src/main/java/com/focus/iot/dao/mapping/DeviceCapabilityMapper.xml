<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceCapabilityMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceCapability">
        <id property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="capabilityType" column="capability_type"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,device_id,process_id,capability_type,description,create_time,update_time
    </sql>

    <!-- 批量保存或更新设备能力 -->
    <insert id="saveOrUpdateDeviceCapabilityBatch">
        INSERT INTO t_device_capability (
        device_id,
        process_id,
        capability_type
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.deviceId},
            #{item.processId},
            #{item.capabilityType}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        device_id = VALUES(device_id),
        process_id = VALUES(process_id),
        capability_type = VALUES(capability_type)
    </insert>
</mapper>
