package com.focus.iot.dao.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.DeviceProcessStatusHistory;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/9/1
 * @Desc
 **/
public interface IDeviceProcessStatusHistoryService extends IService<DeviceProcessStatusHistory> {

    void saveProcessStatusInfo(JSONObject payloadJson);

    long clearProcessStatusHistory(LocalDateTime endTime);

    /**
     * 上传进程数据到OSS
     */
    long uploadProcessStatusDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String region);
}
