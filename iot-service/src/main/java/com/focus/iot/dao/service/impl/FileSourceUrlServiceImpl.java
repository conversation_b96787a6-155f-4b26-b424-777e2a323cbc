package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.FileSourceUrl;
import com.focus.iot.dao.mapper.FileSourceUrlMapper;
import com.focus.iot.dao.service.IFileSourceUrlService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_file_source_url(文件来源URL表)】的数据库操作Service实现
* @createDate 2025-06-27 15:41:39
*/
@Service
public class FileSourceUrlServiceImpl extends ServiceImpl<FileSourceUrlMapper, FileSourceUrl>
    implements IFileSourceUrlService {

}




