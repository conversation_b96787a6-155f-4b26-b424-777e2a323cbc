<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceInfoMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceInfo">
        <id property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="hostname" column="hostname"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="gpuCount" column="gpu_count"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,device_id,hostname,ip_address,gpu_count,status,
        create_time,update_time
    </sql>

    <insert id="saveOrUpdateDeviceInfo">
        INSERT INTO t_device_info (
        id,
        device_id,
        hostname,
        ip_address,
        gpu_count,
        status,
        create_time,
        update_time
        ) VALUES (
        #{id},
        #{deviceId},
        #{hostname},
        #{ipAddress},
        #{gpuCount},
        #{status},
        #{createTime},
        #{updateTime}
        ) ON DUPLICATE KEY UPDATE
        device_id = VALUES(device_id),
        hostname = VALUES(hostname),
        ip_address = VALUES(ip_address),
        gpu_count = VALUES(gpu_count),
        status = VALUES(status),
        create_time = VALUES(create_time),
        update_time = VALUES(update_time)
    </insert>
</mapper>
