package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务记录表
 * @TableName t_task_log
 */
@TableName(value ="t_task_log")
@Data
public class TaskLog {
    /**
     * 任务详情表
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 主机IP
     */
    @TableField(value = "host_ip")
    private String hostIp;

    /**
     * 任务类型
     */
    @TableField(value = "task_type")
    private String taskType;

    /**
     * 任务详情
     */
    @TableField(value = "task_info")
    private String taskInfo;

    /**
     * 任务结果
     */
    @TableField(value = "task_result")
    private String taskResult;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    public TaskLog(String taskId,String hostIp, String taskType, String taskInfo, String taskResult) {
        this.taskId = taskId;
        this.hostIp = hostIp;
        this.taskType = taskType;
        this.taskInfo = taskInfo;
        this.taskResult = taskResult;
        this.createTime = LocalDateTime.now();
    }
}