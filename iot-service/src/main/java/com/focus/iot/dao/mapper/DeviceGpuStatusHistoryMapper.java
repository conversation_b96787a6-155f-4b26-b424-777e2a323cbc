package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.DeviceGpuStatusHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 针对表【t_device_gpu_status_history(GPU 状态历史记录表：记录设备各 GPU 的状态数据快照)】的数据库操作Mapper
 * @createDate 2025-08-25 16:44:05
 * @Entity generator.domain.DeviceGpuStatusHistory
 */
public interface DeviceGpuStatusHistoryMapper extends BaseMapper<DeviceGpuStatusHistory> {
    /**
     * 游标查询：分批次读取所有设备心跳数据
     * 注意：Cursor 查询需在事务中执行（避免连接提前关闭）
     */
    Cursor<DeviceGpuStatusHistory> selectDataByCursor(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

}




