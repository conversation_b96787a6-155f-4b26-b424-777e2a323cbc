package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件来源URL表
 * @TableName t_file_source_url
 */
@TableName(value ="t_file_source_url")
@Data
public class FileSourceUrl {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 该 URL 下载后入库到的 t_file_base_yyyyMM 分表
     */
    @TableField(value = "table_suffix")
    private String tableSuffix;

    /**
     * 任务编号
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * 文件ID
     */
    @TableField(value = "file_id")
    private Long fileId;

    /**
     * 关联文件编号
     */
    @TableField(value = "file_code")
    private String fileCode;

    /**
     * 下载URL
     */
    @TableField(value = "source_url")
    private String sourceUrl;

    /**
     * URL指纹（用于去重）
     */
    @TableField(value = "hash")
    private String hash;

    /**
     * 来源类型：external/internal/manual
     */
    @TableField(value = "source_type")
    private String sourceType;

    /**
     * 是否启用（0=禁用,1=启用）
     */
    @TableField(value = "is_active")
    private Integer isActive;

    /**
     * 下载状态：pending/downloading/success/failed
     */
    @TableField(value = "download_status")
    private String downloadStatus;

    /**
     * 最近下载时间
     */
    @TableField(value = "last_download_time")
    private LocalDateTime lastDownloadTime;

    /**
     * 下载重试次数
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 状态(0-有效,1删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;
}