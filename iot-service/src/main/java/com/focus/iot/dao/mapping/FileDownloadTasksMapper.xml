<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.FileDownloadTasksMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.FileDownloadTasks">
            <id property="id" column="id" />
            <result property="fileCode" column="file_code" />
            <result property="retryCount" column="retry_count" />
            <result property="scheduledAt" column="scheduled_at" />
            <result property="startedAt" column="started_at" />
            <result property="completedAt" column="completed_at" />
            <result property="status" column="status" />
            <result property="errorMsg" column="error_msg" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleted" column="deleted" />
    </resultMap>

    <sql id="Base_Column_List">
        id,file_code,retry_count,scheduled_at,started_at,completed_at,
        status,error_msg,create_time,update_time,deleted
    </sql>
</mapper>
