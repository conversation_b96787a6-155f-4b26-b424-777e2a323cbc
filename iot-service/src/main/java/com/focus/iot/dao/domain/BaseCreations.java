package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户作品集
 *
 * @TableName t_base_creations
 */
@TableName(value = "t_base_creations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseCreations implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1-数字人
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 视频链接
     */
    @TableField(value = "video_url")
    private String videoUrl;

    /**
     * 备用视频链接
     */
    @TableField(value = "backup_video_url")
    private String backupVideoUrl;

    /**
     * 任务编号
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * (0-待处理，1-处理中，2-成功，3-失败)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField(value = "failure_reason")
    private String failureReason;

    /**
     * 数字人链接
     */
    @TableField(value = "digital_human_url")
    private String digitalHumanUrl;

    /**
     * 本地数字人本地文件
     */
    @TableField(value = "local_digital_path")
    private String localDigitalPath;

    /**
     * 音频链接
     */
    @TableField(value = "audio_url")
    private String audioUrl;

    /**
     * 本地音频链接
     */
    @TableField(value = "local_audio_path")
    private String localAudioPath;

    /**
     * 声音ID
     */
    @TableField(value = "voice_id")
    private Integer voiceId;

    /**
     * 文本
     */
    @TableField(value = "text")
    private String text;

    /**
     * 时长(秒)
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    private LocalDateTime modifyTime;

    /**
     * 删除状态(0-正常，1-删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;


    public BaseCreations(Integer type, String title, String taskId, String digitalHumanUrl, String audioUrl, Integer voiceId, String text) {
        this.type = type;
        this.title = title;
        this.taskId = taskId;
        this.audioUrl = audioUrl;
        this.voiceId = voiceId;
        this.text = text;
        this.digitalHumanUrl = digitalHumanUrl;
        this.createTime = LocalDateTime.now();
    }
}