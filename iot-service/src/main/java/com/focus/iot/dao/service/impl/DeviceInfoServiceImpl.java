package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceInfo;
import com.focus.iot.dao.mapper.DeviceInfoMapper;
import com.focus.iot.dao.service.IDeviceInfoService;
import com.focus.iot.util.LocalDateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【t_device_info(设备信息表（仅记录首次出现）)】的数据库操作Service实现
* @createDate 2025-08-25 16:44:05
*/
@Service
public class DeviceInfoServiceImpl extends ServiceImpl<DeviceInfoMapper, DeviceInfo>
    implements IDeviceInfoService {

    @Autowired
    private DeviceInfoMapper deviceInfoMapper;

    @Override
    public void saveDeviceInfo(JSONObject payloadJson, Integer gpuCount) {
        LocalDateTime deviceTime = LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA);
        DeviceInfo deviceInfo = DeviceInfo.builder()
                .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                .gpuCount(gpuCount)
                .hostname(payloadJson.getStr(HeartBeatConstant.HOSTNAME))
                .status(payloadJson.getInt(HeartBeatConstant.STATUS))
                .createTime(deviceTime)
                .updateTime(deviceTime)
                .build();
        this.save(deviceInfo);
    }
}




