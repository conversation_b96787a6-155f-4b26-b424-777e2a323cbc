<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.TaskLogMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.TaskLog">
            <id property="id" column="id" />
            <result property="taskId" column="task_id" />
            <result property="hostIp" column="host_ip" />
            <result property="taskType" column="task_type" />
            <result property="taskInfo" column="task_info" />
            <result property="taskResult" column="task_result" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,task_id,host_ip,task_type,task_info,task_result,create_time
    </sql>
</mapper>
