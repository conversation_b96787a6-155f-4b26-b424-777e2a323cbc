<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceProcessStatusMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceProcessStatus">
        <id property="processId" column="process_id" jdbcType="VARCHAR"/>
        <result property="processType" column="process_type" jdbcType="INTEGER"/>
        <result property="deviceId" column="device_id" jdbcType="VARCHAR"/>
        <result property="gpuIndex" column="gpu_index" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        process_id,process_type,device_id,
        gpu_index,status,last_heartbeat_time,update_time
    </sql>

    <!-- 批量保存或更新设备进程状态 -->
    <insert id="saveOrUpdateDeviceProcessStatusBatch">
        INSERT INTO t_device_process_status (
        process_id,
        process_type,
        device_id,
        gpu_index,
        status,
        last_heartbeat_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.processId},
            #{item.processType},
            #{item.deviceId},
            #{item.gpuIndex},
            #{item.status},
            #{item.lastHeartbeatTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        process_type = VALUES(process_type),
        device_id = VALUES(device_id),
        gpu_index = VALUES(gpu_index),
        status = VALUES(status),
        last_heartbeat_time = VALUES(last_heartbeat_time)
    </insert>

</mapper>
