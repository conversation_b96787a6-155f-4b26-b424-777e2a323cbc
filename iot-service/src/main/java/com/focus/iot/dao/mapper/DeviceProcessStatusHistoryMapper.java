package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.DeviceProcessStatusHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 针对表【t_device_process_status_history(设备进程信息历史记录表)】的数据库操作Mapper
 * @createDate 2025-09-01 14:01:40
 * @Entity com.focus.iot.dao.domain.TDeviceProcessStatusHistory
 */
public interface DeviceProcessStatusHistoryMapper extends BaseMapper<DeviceProcessStatusHistory> {
    /**
     * 游标查询：分批次读取所有设备心跳数据
     * 注意：Cursor 查询需在事务中执行（避免连接提前关闭）
     */
    Cursor<DeviceProcessStatusHistory> selectDataByCursor(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

}
