package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.BaseCreations;
import com.focus.iot.dao.mapper.BaseCreationsMapper;
import com.focus.iot.dao.service.IBaseCreationsService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_base_creations(用户作品集)】的数据库操作Service实现
* @createDate 2025-07-09 19:10:10
*/
@Service
public class BaseCreationsServiceImpl extends ServiceImpl<BaseCreationsMapper, BaseCreations>
    implements IBaseCreationsService {

}




