package com.focus.iot.dao.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.DeviceInfo;

/**
* <AUTHOR>
* @description 针对表【t_device_info(设备信息表（仅记录首次出现）)】的数据库操作Service
* @createDate 2025-08-25 16:44:05
*/
public interface IDeviceInfoService extends IService<DeviceInfo> {
    void saveDeviceInfo(JSONObject payloadJson, Integer gpuCount);
}
