package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.focus.iot.dao.domain.HardwareUsage;
import com.focus.iot.vo.HardwareUsageVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_hardware_usage】的数据库操作Mapper
 * @createDate 2025-04-27 17:54:42
 * @Entity generator.domain.HardwareUsage
 */
public interface HardwareUsageMapper extends BaseMapper<HardwareUsage> {
    /**
     * 根据可选的IP地址查询最新的硬件使用记录
     *
     * @param hostName 可选的IP地址
     * @return 符合条件的最新硬件使用记录列表
     */
    List<HardwareUsageVO> selectLatestByOptionalIp(@Param("hostName") String hostName);
}




