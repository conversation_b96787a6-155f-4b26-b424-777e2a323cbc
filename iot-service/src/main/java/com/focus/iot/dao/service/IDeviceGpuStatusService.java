package com.focus.iot.dao.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.focus.iot.dao.domain.DeviceGpuStatus;

/**
* <AUTHOR>
* @description 针对表【t_device_gpu_status(GPU 实时状态表)】的数据库操作Service
* @createDate 2025-08-25 16:44:05
*/
public interface IDeviceGpuStatusService extends IService<DeviceGpuStatus> {
    void updateGpuStatusInfo(JSONObject payloadJson);
}
