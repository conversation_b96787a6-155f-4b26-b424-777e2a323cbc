package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备能力表：记录每个设备支持的 AI 能力类型
 * @TableName t_device_capability
 */
@TableName(value ="t_device_capability")
@Data
@Builder
public class DeviceCapability {
    /**
     * 能力主键 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备唯一 ID，关联 device 表
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * 进程ID
     */
    @TableField(value = "process_id")
    private String processId;

    /**
     * 能力类型，例如 txt2img、voice_synth 等
     */
    @TableField(value = "capability_type")
    private String capabilityType;

    /**
     * 能力描述信息（可选）
     */
    @TableField(value = "description")
    private String description;

    /**
     * 能力记录创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 能力记录最后更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}