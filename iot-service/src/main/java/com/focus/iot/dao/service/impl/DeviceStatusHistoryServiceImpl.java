package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceStatusHistory;
import com.focus.iot.dao.mapper.DeviceStatusHistoryMapper;
import com.focus.iot.dao.service.IDeviceStatusHistoryService;
import com.focus.iot.enums.UploadFileTypeEnum;
import com.focus.iot.util.LocalDateTimeUtil;
import com.focus.iot.util.UploadOssUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 针对表【t_device_status_history(设备状态历史记录表：记录设备整体资源使用情况)】的数据库操作Service实现
 * @createDate 2025-08-25 16:44:05
 */
@Service
@RequiredArgsConstructor
public class DeviceStatusHistoryServiceImpl extends ServiceImpl<DeviceStatusHistoryMapper, DeviceStatusHistory>
        implements IDeviceStatusHistoryService {

    @Autowired
    private UploadOssUtil uploadOssUtil;

    @Override
    public void saveDeviceStatusInfo(JSONObject payloadJson, BigDecimal gpuUtilAvg) {
        DeviceStatusHistory deviceStatusHistory = DeviceStatusHistory.builder()
                .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                .cpuUsage(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.CPU_USAGE)))
                .memoryUsage(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.MEMORY_USAGE)))
                .diskUsage(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.DISK_USAGE)))
                .gpuUtilAvg(gpuUtilAvg)
                .networkIn(payloadJson.getLong(HeartBeatConstant.NETWORK_IN))
                .networkOut(payloadJson.getLong(HeartBeatConstant.NETWORK_OUT))
                .temperature(BigDecimal.valueOf(payloadJson.getFloat(HeartBeatConstant.TEMPERATURE)))
                .heartbeatTime(LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA))
                .build();
        this.save(deviceStatusHistory);
    }

    @Override
    public long clearDeviceStatusHistory(LocalDateTime endTime) {
        long removeNum = this.count(new QueryWrapper<DeviceStatusHistory>().lt("create_time", endTime));
        this.remove(new QueryWrapper<DeviceStatusHistory>().lt("create_time", endTime));
        return removeNum;
    }

    @Override
    public long uploadDeviceStatusDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String region) {
        // 生成唯一CSV文件名（避免重复）
        String yesterdayStr = LocalDateTimeUtil.getYesterdayDate(LocalDateTimeUtil.YYYYMMDD);
        String fileName = String.format("heartbeat/device/%s/device_status_data_%s.csv", region, yesterdayStr);
        return uploadOssUtil.uploadDataToOSS(startTime, endTime, fileName, UploadFileTypeEnum.DEVICE_STATUS_HISTORY.getType());
    }
}




