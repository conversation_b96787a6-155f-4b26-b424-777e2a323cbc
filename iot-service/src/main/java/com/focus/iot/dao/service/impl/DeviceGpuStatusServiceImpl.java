package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceGpuStatus;
import com.focus.iot.dao.mapper.DeviceGpuStatusMapper;
import com.focus.iot.dao.service.IDeviceGpuStatusService;
import com.focus.iot.util.LocalDateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_device_gpu_status(GPU 实时状态表)】的数据库操作Service实现
* @createDate 2025-08-25 16:44:05
*/
@Service
public class DeviceGpuStatusServiceImpl extends ServiceImpl<DeviceGpuStatusMapper, DeviceGpuStatus>
    implements IDeviceGpuStatusService {

    @Autowired
    private DeviceGpuStatusMapper deviceGpuStatusMapper;

    @Override
    public void updateGpuStatusInfo(JSONObject payloadJson) {
        // 1、获取gpu数组（JSONArray类型）
        JSONArray gpuArray = payloadJson.getJSONArray(HeartBeatConstant.GPU);
        List<DeviceGpuStatus> gpuStatusList = new ArrayList<>();

        // 2、遍历
        for (int i = 0; i < gpuArray.size(); i++) {
            JSONObject gpuJson = gpuArray.getJSONObject(i);
            // 3、实时更新GPU数据
            DeviceGpuStatus gpuStatus = DeviceGpuStatus.builder()
                    .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                    .gpuIndex(gpuJson.getInt(HeartBeatConstant.GPU_INDEX))
                    .gpuName(gpuJson.getStr(HeartBeatConstant.GPU_NAME))
                    .memoryTotal(gpuJson.getInt(HeartBeatConstant.MEMORY_TOTAL))
                    .memoryUsed(gpuJson.getInt(HeartBeatConstant.MEMORY_USED))
                    .utilization(BigDecimal.valueOf(gpuJson.getFloat(HeartBeatConstant.UTILIZATION)))
                    .temperature(BigDecimal.valueOf(gpuJson.getFloat(HeartBeatConstant.TEMPERATURE)))
                    .powerDraw(BigDecimal.valueOf(gpuJson.getFloat(HeartBeatConstant.POWER_DRAW)))
                    .lastHeartbeatTime(LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA))
                    .build();
            gpuStatusList.add(gpuStatus);
        }
        deviceGpuStatusMapper.saveOrUpdateDeviceGpuStatusBatch(gpuStatusList);
    }
}




