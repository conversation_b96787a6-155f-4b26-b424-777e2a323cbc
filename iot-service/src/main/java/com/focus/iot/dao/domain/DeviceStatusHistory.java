package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备状态历史记录表：记录设备整体资源使用情况
 *
 * @TableName t_device_status_history
 */
@TableName(value = "t_device_status_history")
@Data
@Builder
public class DeviceStatusHistory {
    /**
     * 主键 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备唯一 ID
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * CPU 使用率（百分比）
     */
    @TableField(value = "cpu_usage")
    private BigDecimal cpuUsage;

    /**
     * 内存使用率（百分比）
     */
    @TableField(value = "memory_usage")
    private BigDecimal memoryUsage;

    /**
     * 磁盘使用率（百分比）
     */
    @TableField(value = "disk_usage")
    private BigDecimal diskUsage;

    /**
     * GPU 平均利用率（百分比）
     */
    @TableField(value = "gpu_util_avg")
    private BigDecimal gpuUtilAvg;

    /**
     * 网络流入（单位：字节）
     */
    @TableField(value = "network_in")
    private Long networkIn;

    /**
     * 网络流出（单位：字节）
     */
    @TableField(value = "network_out")
    private Long networkOut;

    /**
     * 设备温度（单位：℃）
     */
    @TableField(value = "temperature")
    private BigDecimal temperature;

    /**
     * 心跳上报时间
     */
    @TableField(value = "heartbeat_time")
    private LocalDateTime heartbeatTime;

    /**
     * 记录创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
}