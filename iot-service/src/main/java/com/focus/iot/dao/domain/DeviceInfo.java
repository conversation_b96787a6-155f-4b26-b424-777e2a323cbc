package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备信息表（仅记录首次出现）
 * @TableName t_device_info
 */
@TableName(value ="t_device_info")
@Data
@Builder
public class DeviceInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备 ID（系统唯一标识）
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * 主机名
     */
    @TableField(value = "hostname")
    private String hostname;

    /**
     * IP 地址
     */
    @TableField(value = "ip_address")
    private String ipAddress;

    /**
     * GPU 数量
     */
    @TableField(value = "gpu_count")
    private Integer gpuCount;

    /**
     * 状态：0-在线，1-离线
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 首次上报时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}