package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备进程信息实时状态表
 * @TableName t_device_process_status
 */
@Data
@Builder
@TableName(value ="t_device_process_status")
public class DeviceProcessStatus {
    /**
     * 进程ID
     */
    @TableField(value = "process_id")
    private String processId;

    /**
     * 进程类型：主进程1；子进程2
     */
    @TableField(value = "process_type")
    private Integer processType;

    /**
     * 设备 ID
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * GPU 编号，从 0 开始
     */
    @TableField(value = "gpu_index")
    private Integer gpuIndex;

    /**
     * 进程当前的存活状态：1运行中；2启动中；3已停止
     */
    @TableField(value = "status")
    private Integer status;

        /**
     * 最后心跳上报时间
     */
    @TableField(value = "last_heartbeat_time")
    private LocalDateTime lastHeartbeatTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}