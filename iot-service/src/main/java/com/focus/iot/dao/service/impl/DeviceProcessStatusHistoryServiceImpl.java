package com.focus.iot.dao.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.domain.DeviceProcessStatusHistory;
import com.focus.iot.dao.mapper.DeviceProcessStatusHistoryMapper;
import com.focus.iot.dao.service.IDeviceProcessStatusHistoryService;
import com.focus.iot.enums.UploadFileTypeEnum;
import com.focus.iot.util.LocalDateTimeUtil;
import com.focus.iot.util.UploadOssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/9/1
 * @Desc
 **/
@Service
public class DeviceProcessStatusHistoryServiceImpl extends ServiceImpl<DeviceProcessStatusHistoryMapper, DeviceProcessStatusHistory>
        implements IDeviceProcessStatusHistoryService {

    @Autowired
    private UploadOssUtil uploadOssUtil;

    @Override
    public void saveProcessStatusInfo(JSONObject payloadJson) {
        // 1、获取gpu数组（JSONArray类型）
        JSONArray processArray = payloadJson.getJSONArray(HeartBeatConstant.PROCESS);
        List<DeviceProcessStatusHistory> processStatusHistoryList = new ArrayList<>();
        if(processArray == null || processArray.isEmpty()){
            return;
        }
        
        // 2、遍历
        for (int i = 0; i < processArray.size(); i++) {
            JSONObject processJson = processArray.getJSONObject(i);
            // GPU心跳信息
            DeviceProcessStatusHistory processStatus = DeviceProcessStatusHistory.builder()
                    .processId(processJson.getStr(HeartBeatConstant.PROCESS_ID))
                    .processType(processJson.getInt(HeartBeatConstant.PROCESS_TYPE))
                    .deviceId(payloadJson.getStr(HeartBeatConstant.DEVICE_ID))
                    .gpuIndex(processJson.getInt(HeartBeatConstant.GPU_INDEX))
                    .status(processJson.getInt(HeartBeatConstant.PROCESS_STATUS))
                    .heartbeatTime(LocalDateTimeUtil.formatGMT8(payloadJson.getStr(HeartBeatConstant.TIMESTAMP), LocalDateTimeUtil.ASIA))
                    .build();
            processStatusHistoryList.add(processStatus);
        }

        // 3、保存到GPU历史记录表
        this.saveBatch(processStatusHistoryList);
    }

    @Override
    public long clearProcessStatusHistory(LocalDateTime endTime) {
        long removeNum = this.count(new QueryWrapper<DeviceProcessStatusHistory>().lt("create_time", endTime));
        this.remove(new QueryWrapper<DeviceProcessStatusHistory>().lt("create_time", endTime));
        return removeNum;
    }

    @Override
    public long uploadProcessStatusDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String region) {
        // 生成唯一CSV文件名（避免重复）
        String yesterdayStr = LocalDateTimeUtil.getYesterdayDate(LocalDateTimeUtil.YYYYMMDD);
        String fileName = String.format("heartbeat/process/%s/process_status_data_%s.csv", region, yesterdayStr);
        return uploadOssUtil.uploadDataToOSS(startTime, endTime, fileName, UploadFileTypeEnum.PROCESS_STATUS_HISTORY.getType());
    }
}
