<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.ConfigMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.Config">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="isEdit" column="is_edit" jdbcType="TINYINT"/>
        <result property="serialNum" column="serial_num" jdbcType="INTEGER"/>
        <result property="flag" column="flag" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="modifyTime" column="modify_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,`type`,`name`,
        `value`,remark,is_edit,
        serial_num,flag,create_time,
        modify_time,deleted
    </sql>
</mapper>
