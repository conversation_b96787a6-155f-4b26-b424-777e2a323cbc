package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 通用文件基础信息表
 * @TableName t_file_base_202506
 */
@Data
@TableName(value = "t_file_base", autoResultMap = true) // 注意：逻辑表名
public class FileBase {
    /**
     * 文件ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对外文件编号
     */
    @TableField(value = "file_code")
    private String fileCode;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件类型(扩展名)
     */
    @TableField(value = "file_type")
    private String fileType;

    /**
     * 文件大小(兆)
     */
    @TableField(value = "size")
    private BigDecimal size;

    /**
     * 本地存储路径
     */
    @TableField(value = "local_path")
    private String localPath;

    /**
     * 远程文件路径
     */
    @TableField(value = "source_url")
    private String sourceUrl;

    /**
     * 下载状态(pending/success/failed)
     */
    @TableField(value = "download_status")
    private String downloadStatus;

    /**
     * 下载失败原因
     */
    @TableField(value = "error_msg")
    private String errorMsg;

    /**
     * 请求时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 状态(0-有效,1删除)
     */
    @TableField(value = "deleted")
    private Integer deleted;
}