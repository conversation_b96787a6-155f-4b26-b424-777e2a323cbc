package com.focus.iot.dao.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.framework.constants.CommonConstant;
import com.focus.framework.enums.system.ConfigSwitchEnums;
import com.focus.iot.constant.RedisConstant;
import com.focus.iot.dao.domain.Config;
import com.focus.iot.dao.mapper.ConfigMapper;
import com.focus.iot.dao.service.IConfigService;
import com.focus.iot.service.IRedisBizService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static cn.hutool.core.text.CharPool.COLON;

/**
 * <AUTHOR>
 * @description 针对表【t_config(配置表)】的数据库操作Service实现
 * @createDate 2023-08-20 10:53:55
 */
@Service
@RequiredArgsConstructor
public class ConfigServiceImpl extends ServiceImpl<ConfigMapper, Config>
        implements IConfigService {
    private final IRedisBizService redisService;

    @Override
    public List<Config> getGpList(String type, String name) {

        // 缓存key
        String cacheKey = Joiner.on(COLON).skipNulls().join(RedisConstant.CONFIG_KEY, type, name);

        // 缓存
        String value = redisService.getValue(cacheKey);
        if (StrUtil.isNotBlank(value)) {
            JSONArray array = JSONUtil.parseArray(value);
            return array.toList(Config.class);
        }

        // 查询数据库
        List<Config> list = this.list(new QueryWrapper<Config>().lambda()
                .eq(StrUtil.isNotBlank(type), Config::getType, type)
                .eq(StrUtil.isNotBlank(name), Config::getName, name));

        // 放缓存
        redisService.cacheValue(cacheKey, JSONUtil.toJsonStr(list));

        return list;
    }

    @Override
    public Config getConfig(String name) {
        List<Config> list = this.getGpList(CommonConstant.CONFIG_DEFAULT_GP, name);
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public Config getConfig(String gp, String name) {
        List<Config> list = this.getGpList(gp, name);
        if (CollUtil.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public String getTextConfig(String name) {
        Config config = this.getConfig(name);
        if (config != null) {
            return config.getValue();
        }
        return null;
    }

    @Override
    public String getTextConfig(String gp, String name) {
        Config config = this.getConfig(gp, name);
        if (config != null) {
            return config.getValue();
        }
        return null;
    }

    @Override
    public Boolean getSwitchConfig(String name) {
        return StrUtil.equals(this.getTextConfig(name), ConfigSwitchEnums.OPEN.getCode());
    }
}




