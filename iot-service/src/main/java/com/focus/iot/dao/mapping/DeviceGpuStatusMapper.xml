<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceGpuStatusMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceGpuStatus">
        <id property="deviceId" column="device_id"/>
        <id property="gpuIndex" column="gpu_index"/>
        <result property="gpuName" column="gpu_name"/>
        <result property="utilization" column="utilization"/>
        <result property="memoryTotal" column="memory_total"/>
        <result property="memoryUsed" column="memory_used"/>
        <result property="temperature" column="temperature"/>
        <result property="powerDraw" column="power_draw"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        device_id,gpu_index,gpu_name,utilization,memory_total,memory_used,
        temperature,power_draw,last_heartbeat_time,update_time
    </sql>

    <!-- 有则更新，无则插入 -->
    <insert id="saveOrUpdateDeviceGpuStatus">
        INSERT INTO device_gpu_status (
        device_id,
        gpu_index,
        gpu_name,
        utilization,
        memory_total,
        memory_used,
        temperature,
        power_draw,
        last_heartbeat_time
        ) VALUES (
        #{deviceId},
        #{gpuIndex},
        #{gpuName},
        #{utilization},
        #{memoryTotal},
        #{memoryUsed},
        #{temperature},
        #{powerDraw},
        #{lastHeartbeatTime}
        ) ON DUPLICATE KEY UPDATE
        gpu_name = VALUES(gpu_name),
        utilization = VALUES(utilization),
        memory_total = VALUES(memory_total),
        memory_used = VALUES(memory_used),
        temperature = VALUES(temperature),
        power_draw = VALUES(power_draw),
        last_heartbeat_time = VALUES(last_heartbeat_time)
    </insert>

    <insert id="saveOrUpdateDeviceGpuStatusBatch">
        INSERT INTO t_device_gpu_status (
        device_id,
        gpu_index,
        gpu_name,
        utilization,
        memory_total,
        memory_used,
        temperature,
        power_draw,
        last_heartbeat_time
        ) VALUES
        <!-- 遍历List集合，生成多个值列表 -->
        <foreach collection="list" item="item" separator=",">
            (
            #{item.deviceId},
            #{item.gpuIndex},
            #{item.gpuName},
            #{item.utilization},
            #{item.memoryTotal},
            #{item.memoryUsed},
            #{item.temperature},
            #{item.powerDraw},
            #{item.lastHeartbeatTime}
            )
        </foreach>
        <!-- 批量更新逻辑：当(device_id, gpu_index)联合主键存在时触发 -->
        ON DUPLICATE KEY UPDATE
        gpu_name = VALUES(gpu_name),
        utilization = VALUES(utilization),
        memory_total = VALUES(memory_total),
        memory_used = VALUES(memory_used),
        temperature = VALUES(temperature),
        power_draw = VALUES(power_draw),
        last_heartbeat_time = VALUES(last_heartbeat_time)
    </insert>
</mapper>
