<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceAlarmMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceAlarm">
            <id property="id" column="id" />
            <result property="deviceId" column="device_id" />
            <result property="alarmType" column="alarm_type" />
            <result property="alarmLevel" column="alarm_level" />
            <result property="alarmMessage" column="alarm_message" />
            <result property="alarmStatus" column="alarm_status" />
            <result property="alarmTime" column="alarm_time" />
            <result property="confirmTime" column="confirm_time" />
            <result property="resolvedTime" column="resolved_time" />
            <result property="createdBy" column="created_by" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,device_id,alarm_type,alarm_level,alarm_message,alarm_status,
        alarm_time,confirm_time,resolved_time,created_by,create_time,
        update_time
    </sql>
</mapper>
