<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceStatusHistoryMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceStatusHistory">
        <id property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="cpuUsage" column="cpu_usage"/>
        <result property="memoryUsage" column="memory_usage"/>
        <result property="diskUsage" column="disk_usage"/>
        <result property="gpuUtilAvg" column="gpu_util_avg"/>
        <result property="networkIn" column="network_in"/>
        <result property="networkOut" column="network_out"/>
        <result property="temperature" column="temperature"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,device_id,cpu_usage,memory_usage,disk_usage,gpu_util_avg,
        network_in,network_out,temperature,create_time
    </sql>

    <select id="selectDataByCursor" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_device_status_history
        WHERE create_time &gt; #{startTime} AND create_time &lt; #{endTime}
        ORDER BY id
    </select>
</mapper>
