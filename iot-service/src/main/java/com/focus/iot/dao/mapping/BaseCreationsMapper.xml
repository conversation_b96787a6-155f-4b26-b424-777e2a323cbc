<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.BaseCreationsMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.BaseCreations">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="videoUrl" column="video_url"/>
        <result property="backupVideoUrl" column="backup_video_url"/>
        <result property="taskId" column="task_id"/>
        <result property="status" column="status"/>
        <result property="failureReason" column="failure_reason"/>
        <result property="digitalHumanUrl" column="digital_human_url"/>
        <result property="localDigitalPath" column="local_digital_path"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="localAudioPath" column="local_audio_path"/>
        <result property="voiceId" column="voice_id"/>
        <result property="text" column="text"/>
        <result property="duration" column="duration"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,`type`,title,video_url,backup_video_url,task_id,
        status,failure_reason,digital_human_url,local_digital_path,audio_url,local_audio_path,voice_id,
        text,duration,create_time,modify_time,deleted
    </sql>
</mapper>
