package com.focus.iot.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.cursor.Cursor;
import com.focus.iot.dao.domain.DeviceStatusHistory;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【t_device_status_history(设备状态历史记录表：记录设备整体资源使用情况)】的数据库操作Mapper
* @createDate 2025-08-25 16:44:05
* @Entity generator.domain.DeviceStatusHistory
*/
public interface DeviceStatusHistoryMapper extends BaseMapper<DeviceStatusHistory> {
    /**
     * 游标查询：分批次读取所有设备心跳数据
     * 注意：Cursor 查询需在事务中执行（避免连接提前关闭）
     */
    Cursor<DeviceStatusHistory> selectDataByCursor(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

}




