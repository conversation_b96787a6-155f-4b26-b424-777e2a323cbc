<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceGpuStatusHistoryMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceGpuStatusHistory">
            <id property="id" column="id" />
            <result property="deviceId" column="device_id" />
            <result property="gpuIndex" column="gpu_index" />
            <result property="utilization" column="utilization" />
            <result property="memoryUsed" column="memory_used" />
            <result property="temperature" column="temperature" />
            <result property="powerDraw" column="power_draw" />
            <result property="heartbeatTime" column="heartbeat_time" />
            <result property="createTime" column="create_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,device_id,gpu_index,utilization,memory_used,temperature,heartbeat_time,
        power_draw,create_time
    </sql>

    <select id="selectDataByCursor" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_device_gpu_status_history
        WHERE create_time &gt; #{startTime} AND create_time &lt; #{endTime}
        ORDER BY id
    </select>
</mapper>
