<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.DeviceStatusMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.DeviceStatus">
            <id property="deviceId" column="device_id" />
            <result property="status" column="status" />
            <result property="cpuUsage" column="cpu_usage" />
            <result property="memoryUsage" column="memory_usage" />
            <result property="diskUsage" column="disk_usage" />
            <result property="gpuUtilAvg" column="gpu_util_avg" />
            <result property="gpuMemAvg" column="gpu_mem_avg" />
            <result property="networkIn" column="network_in" />
            <result property="networkOut" column="network_out" />
            <result property="temperature" column="temperature" />
            <result property="lastHeartbeatTime" column="last_heartbeat_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        device_id,status,cpu_usage,memory_usage,disk_usage,gpu_util_avg,
        gpu_mem_avg,network_in,network_out,temperature,last_heartbeat_time,
        update_time
    </sql>
    <insert id="saveOrUpdateDeviceStatus">
    INSERT INTO t_device_status (
        device_id,
        status,
        cpu_usage,
        memory_usage,
        disk_usage,
        gpu_util_avg,
        gpu_mem_avg,
        network_in,
        network_out,
        temperature,
        last_heartbeat_time
    ) VALUES (
        #{deviceId},
        #{status},
        #{cpuUsage},
        #{memoryUsage},
        #{diskUsage},
        #{gpuUtilAvg},
        #{gpuMemAvg},
        #{networkIn},
        #{networkOut},
        #{temperature},
        #{lastHeartbeatTime}
    ) ON DUPLICATE KEY UPDATE
        status = VALUES(status),
        cpu_usage = VALUES(cpu_usage),
        memory_usage = VALUES(memory_usage),
        disk_usage = VALUES(disk_usage),
        gpu_util_avg = VALUES(gpu_util_avg),
        gpu_mem_avg = VALUES(gpu_mem_avg),
        network_in = VALUES(network_in),
        network_out = VALUES(network_out),
        temperature = VALUES(temperature),
        last_heartbeat_time = VALUES(last_heartbeat_time)
    </insert>
</mapper>
