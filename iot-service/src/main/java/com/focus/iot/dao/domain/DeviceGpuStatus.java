package com.focus.iot.dao.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * GPU 实时状态表
 *
 * @TableName t_device_gpu_status
 */
@TableName(value = "t_device_gpu_status")
@Data
@Builder
public class DeviceGpuStatus {
    /**
     * 设备 ID
     */
    @TableField(value = "device_id")
    private String deviceId;

    /**
     * GPU 编号，从 0 开始
     */
    @TableField(value = "gpu_index")
    private Integer gpuIndex;

    /**
     * GPU 型号，如 A100
     */
    @TableField(value = "gpu_name")
    private String gpuName;

    /**
     * GPU 使用率（%）
     */
    @TableField(value = "utilization")
    private BigDecimal utilization;

    /**
     * 总显存（MB）
     */
    @TableField(value = "memory_total")
    private Integer memoryTotal;

    /**
     * 已用显存（MB）
     */
    @TableField(value = "memory_used")
    private Integer memoryUsed;

    /**
     * 温度（℃）
     */
    @TableField(value = "temperature")
    private BigDecimal temperature;

    /**
     * 功耗（W）
     */
    @TableField(value = "power_draw")
    private BigDecimal powerDraw;

    /**
     * 最后心跳上报时间
     */
    @TableField(value = "last_heartbeat_time")
    private LocalDateTime lastHeartbeatTime;

    /**
     * 记录更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}