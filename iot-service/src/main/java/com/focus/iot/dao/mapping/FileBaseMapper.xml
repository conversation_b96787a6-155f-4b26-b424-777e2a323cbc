<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.focus.iot.dao.mapper.FileBaseMapper">

    <resultMap id="BaseResultMap" type="com.focus.iot.dao.domain.FileBase">
        <id property="id" column="id"/>
        <result property="fileCode" column="file_code"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="size" column="size"/>
        <result property="localPath" column="local_path"/>
        <result property="sourceUrl" column="source_url"/>
        <result property="downloadStatus" column="download_status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,file_code,file_name,file_type,size,local_path,
        source_url,download_status,error_msg,create_time,update_time,
        deleted
    </sql>

    <select id="selectPage" resultType="FileBase">
        SELECT * FROM ${TABLE_NAME}
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_time DESC
    </select>

</mapper>
