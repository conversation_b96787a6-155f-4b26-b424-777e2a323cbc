package com.focus.iot.dao.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.focus.iot.dao.domain.HardwareUsage;
import com.focus.iot.dao.mapper.HardwareUsageMapper;
import com.focus.iot.dao.service.IHardwareUsageService;
import com.focus.iot.vo.HardwareUsageVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_hardware_usage】的数据库操作Service实现
* @createDate 2025-04-27 17:54:42
*/
@Service
public class HardwareUsageServiceImpl extends ServiceImpl<HardwareUsageMapper, HardwareUsage>
    implements IHardwareUsageService {

    @Override
    public List<HardwareUsageVO> selectLatestByOptionalIp(String hostName) {
        List<HardwareUsageVO> hardwareUsages = baseMapper.selectLatestByOptionalIp(hostName);
        return hardwareUsages;
    }
}




