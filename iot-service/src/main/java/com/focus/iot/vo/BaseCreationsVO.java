package com.focus.iot.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class BaseCreationsVO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1-数字人
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 视频链接
     */
    @TableField(value = "video_url")
    private String videoUrl;

    /**
     * 备用视频链接
     */
    @TableField(value = "backup_video_url")
    private String backupVideoUrl;

    /**
     * 任务编号
     */
    @TableField(value = "task_id")
    private String taskId;

    /**
     * (0-待处理，1-处理中，2-成功，3-失败)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField(value = "failure_reason")
    private String failureReason;

    /**
     * 时长(秒)
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "modify_time")
    private LocalDateTime modifyTime;
}
