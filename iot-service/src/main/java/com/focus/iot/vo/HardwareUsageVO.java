package com.focus.iot.vo;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class HardwareUsageVO {
    /**
     * 监控主键
     */
    private Long id;

    /**
     * 主机名/IP 地址主机
     */
    private String hostName;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * cpu使用率
     */
    private BigDecimal cpuUsage;

    /**
     * 总内存(GB)
     */
    private BigDecimal memoryTotal;

    /**
     * 已用内存(GB)
     */
    private BigDecimal memoryUsed;

    /**
     * 内存使用率
     */
    private BigDecimal memoryUsage;

    /**
     * 磁盘已用(G)
     */
    private BigDecimal diskUsed;

    /**
     * 磁盘总量(G)
     */
    private BigDecimal diskTotal;

    /**
     * 磁盘使用率
     */
    private BigDecimal diskUsage;

    /**
     * GPU 显存已用 (MB)
     */
    private BigDecimal gpuMemoryUsed;

    /**
     * GPU 显存总量 (MB)
     */
    private BigDecimal gpuMemoryTotal;

    /**
     * GPU 显存使用率
     */
    private BigDecimal gpuMemoryUsage;

    /**
     * GPU 算力使用率
     */
    private BigDecimal gpuLoad;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime createTime;
}
