package com.focus.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/8/29
 * @Desc websocktet 消息类型枚举
 **/
@Getter
@AllArgsConstructor
public enum WebsocketTypeEnum {
    REGISTER("register", "注册"),
    RESULT("result", "结果"),
    CONFIG("config", "配置文件"),
    HEARTBEAT("heartbeat", "心跳"),
    INVOKE("invoke", "调用"),
    CONTROL("control", "操作"),
    ;

    private final String name;
    private final String desc;
}
