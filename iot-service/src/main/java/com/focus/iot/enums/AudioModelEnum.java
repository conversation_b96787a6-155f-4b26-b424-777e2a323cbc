package com.focus.iot.enums;

public enum AudioModelEnum {
    ALL_ZH("all_zh", "中文", "达摩 ASR (中文)", "large", "zh"),
    ALL_YUE("all_yue", "粤语", "达摩 ASR (中文)", "large", "yue"),
    EN("en", "英文", "Faster Whisper (多语种)", "large-v3", "en"),
    ALL_JA("all_ja", "日文", "Faster Whisper (多语种)", "large-v3", "ja"),
    ALL_KO("all_ko", "韩文", "Faster Whisper (多语种)", "large-v3", "ko"),
    ;

    private String lang;
    private String desc;
    private String asrModel;
    private String asrSize;
    private String asrLang;

    private AudioModelEnum(String lang, String desc, String asrModel, String asrSize, String asrLang) {
        this.lang = lang;
        this.desc = desc;
        this.asrModel = asrModel;
        this.asrSize = asrSize;
        this.asrLang = asrLang;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getAsrModel() {
        return asrModel;
    }

    public void setAsrModel(String asrModel) {
        this.asrModel = asrModel;
    }

    public String getAsrSize() {
        return asrSize;
    }

    public void setAsrSize(String asrSize) {
        this.asrSize = asrSize;
    }

    public String getAsrLang() {
        return asrLang;
    }

    public void setAsrLang(String asrLang) {
        this.asrLang = asrLang;
    }

    public static AudioModelEnum getInfoByLang(String lang) {
        for (AudioModelEnum audioModelEnum : values()) {
            if (audioModelEnum.getLang().equals(lang)) {
                return audioModelEnum;
            }
        }
        return null;
    }
}
