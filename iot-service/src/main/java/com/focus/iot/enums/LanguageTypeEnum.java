package com.focus.iot.enums;


public enum LanguageTypeEnum {

    CHINESE(0, "中文", "all_zh"),
    ENGLISH(1, "英文", "en"),             // 👈 英文排在第 2 行
    CANTONESE(2, "粤语", "all_yue"),
    JAPANESE(3, "日文", "all_ja"),
    KOREAN(4, "韩文", "all_ko"),
    CHINESE_ENGLISH_MIXED(5, "中英混合", "zh"),
    CANTONESE_ENGLISH_MIXED(6, "粤英混合", "yue"),
    JAPANESE_ENGLISH_MIXED(7, "日英混合", "ja"),
    KOREAN_ENGLISH_MIXED(8, "韩英混合", "ko"),
    MULTILINGUAL_MIXED(9, "多语种混合", "auto"),
    MULTILINGUAL_MIXED_CANTONESE(10, "多语种混合(粤语)", "auto_yue"),

    // 以下是 value 别名映射（可选项）
    ALL_ZH(11, "all_zh", "all_zh"),
    ALL_YUE(12, "all_yue", "all_yue"),
    EN(13, "en", "en"),
    ALL_JA(14, "all_ja", "all_ja"),
    ALL_KO(15, "all_ko", "all_ko"),
    ZH(16, "zh", "zh"),
    YUE(17, "yue", "yue"),
    JA(18, "ja", "ja"),
    KO(19, "ko", "ko"),
    AUTO(20, "auto", "auto"),
    AUTO_YUE(21, "auto_yue", "auto_yue");

    private final int code;
    private final String label;
    private final String value;

    LanguageTypeEnum(int code, String label, String value) {
        this.code = code;
        this.label = label;
        this.value = value;
    }

    public int getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    public static LanguageTypeEnum fromValue(String value) {
        for (LanguageTypeEnum type : values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        return null;
    }

    public static LanguageTypeEnum fromLabel(String label) {
        for (LanguageTypeEnum type : values()) {
            if (type.label.equalsIgnoreCase(label)) {
                return type;
            }
        }
        return null;
    }

    public static LanguageTypeEnum fromCode(int code) {
        for (LanguageTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
