package com.focus.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/9/3
 * @desc 进程状态 状态机 四种状态：1、运行中；2、启动中；3、已暂停；4、已关闭
 **/
@Getter
@AllArgsConstructor
public enum ProcessStatusEnum {
    RUNNING(1, "运行中"),
    STARTING(2, "启动中"),
    STOPPED(3, "已暂停"),
    SHUTDOWN(4, "已关闭"),
    ;

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举值
     * @param code 状态码
     * @return 对应的枚举值，如果没有找到则返回null
     */
    public static ProcessStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProcessStatusEnum status : ProcessStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}
