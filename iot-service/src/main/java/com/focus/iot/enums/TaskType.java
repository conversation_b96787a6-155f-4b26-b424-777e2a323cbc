package com.focus.iot.enums;

public enum TaskType {
    DIGITAL_HUMAN("digital_human", "数字人生成"),
    TEXT2IMAGE("text2image", "文生图"),
    IMAGE2IMAGE("image2image", "图生图"),
    TEXT2VIDEO("text2video", "文生视频"),
    VIDEO2VIDEO("video2video", "视频生视频"),
    TEXT2SPEECH("text2speech", "文本转语音"),
    HEARTBEAT("heartbeat", "心跳包"),
    CANCEL_TASK("cancel_task", "取消任务");

    private final String type;
    private final String description;

    TaskType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return String.format("TaskType{type='%s', description='%s'}", type, description);
    }

    public static TaskType fromString(String type) {
        for (TaskType taskType : TaskType.values()) {
            if (taskType.getType().equalsIgnoreCase(type)) {
                return taskType;
            }
        }
        throw new IllegalArgumentException("Unknown task type: " + type);
    }
}
