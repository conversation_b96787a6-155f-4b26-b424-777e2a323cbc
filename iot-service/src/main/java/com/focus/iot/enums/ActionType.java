package com.focus.iot.enums;

public enum ActionType {
    START("start", "启动任务"),
    STOP("stop", "停止任务"),
    CANCEL("cancel", "取消任务");

    private final String action;
    private final String description;

    ActionType(String action, String description) {
        this.action = action;
        this.description = description;
    }

    public String getAction() {
        return action;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return String.format("ActionType{action='%s', description='%s'}", action, description);
    }

    public static ActionType fromString(String action) {
        for (ActionType actionType : ActionType.values()) {
            if (actionType.getAction().equalsIgnoreCase(action)) {
                return actionType;
            }
        }
        throw new IllegalArgumentException("Unknown action type: " + action);
    }
}
