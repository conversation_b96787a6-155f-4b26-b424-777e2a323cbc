package com.focus.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Random;

@Getter
@AllArgsConstructor
public enum ErrorMsgEnum {

    TRAINING_TIMES(-1, "您当前数字人剩余次数不足，请先进行充值~"),
    CURRENT_TRAINING_TIMES(-1, "您当前形象定制数量不足，请先进行充值~"),

    ;


    private Integer code;
    private String desc;

    public static String getRandomDesc() {
        Random random = new Random();
        int index = random.nextInt(values().length);
        return values()[index].getDesc();
    }
}
