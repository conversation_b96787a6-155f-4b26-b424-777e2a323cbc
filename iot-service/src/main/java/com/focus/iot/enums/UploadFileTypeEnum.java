package com.focus.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/8/29
 * @Desc
 **/
@Getter
@AllArgsConstructor
public enum UploadFileTypeEnum {
    DEVICE_STATUS_HISTORY("DeviceStatusHistory", "设备心跳历史数据"),
    GPU_STATUS_HISTORY("DeviceGpuStatusHistory", "GPU心跳历史数据"),
    PROCESS_STATUS_HISTORY("DeviceProcessStatusHistory", "进程心跳历史数据"),
    ;

    private final String type;
    private final String description;
}
