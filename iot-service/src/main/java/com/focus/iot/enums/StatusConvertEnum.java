package com.focus.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum StatusConvertEnum {
    ONT(1, 2, "待确认~"),
    TWO(2, 3, "已生效~"),
    THREE(3, 4, "失败~"),
    ;


    private Integer status;
    private Integer newStatus;
    private String desc;

    public static Integer getNewStatusByCode(Integer status) {
        for (StatusConvertEnum bizTypeEnum : values()) {
            if (bizTypeEnum.getStatus().equals(status)) {
                return bizTypeEnum.newStatus;
            }
        }
        return -1;
    }
}
