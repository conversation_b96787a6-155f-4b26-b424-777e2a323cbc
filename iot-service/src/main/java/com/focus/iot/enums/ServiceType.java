package com.focus.iot.enums;

// 定义服务类型和端口范围
public enum ServiceType {
    DIGITAL_HUMAN("数字人扫描", 14000, 14499, "/service/status"),
    V2_TRAINING("V2训练", 14500, 14599, "/service/status"),
    V2_INFERENCE("V2推理", 14600, 14999, "/service/status"),
    V4_TRAINING("V4训练", 15000, 15099, "/status"),
    V4_INFERENCE("V4推理", 15100, 15499, "/status");

    final String name;
    final int minPort;
    final int maxPort;
    final String apiPath;

    ServiceType(String name, int minPort, int maxPort, String apiPath) {
        this.name = name;
        this.minPort = minPort;
        this.maxPort = maxPort;
        this.apiPath = apiPath;
    }
}