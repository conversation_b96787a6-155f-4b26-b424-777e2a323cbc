package com.focus.iot.websocket;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.focus.framework.dto.Result;
import com.focus.framework.enums.ResultCode;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dto.RegisterResponseDTO;
import com.focus.iot.enums.WebsocketTypeEnum;
import com.focus.iot.registry.SessionRegistry;
import com.focus.iot.service.IDeviceInfoBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;

@Slf4j
@Component
public class WsHandler extends TextWebSocketHandler {

    @Value("${websocket.base.user-name}")
    private String websocketBaseUserName;

    @Value("${websocket.base.password}")
    private String websocketBasePassword;

    @Autowired
    private WsClientManager wsClientManager;

    private final IDeviceInfoBizService deviceService;

    private final SessionRegistry sessionRegistry;

    // ✅ 注入 SessionRegistry
    public WsHandler(IDeviceInfoBizService deviceService, SessionRegistry sessionRegistry) {
        this.deviceService = deviceService;
        this.sessionRegistry = sessionRegistry;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        // 等待客户端 register
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String msg = message.getPayload();
        JSONObject json = new JSONObject(msg);
        String type = json.getStr(HeartBeatConstant.TYPE);

        switch (type) {
            case "register":
                handleRegister(session, json);
                break;
            case "invoke":
            case "heartbeat":
            case "result":
                if (!sessionRegistry.isAuthenticated(session)) {
                    rejectSession(session, ResultCode.CLIENT_AUTH_FAILED.getMessage());
                    return;
                }
                switch (type) {
                    case "invoke":
                        handleInvoke(session, json);
                        break;
                    case "heartbeat":
                        handleHeartbeat(session, json);
                        break;
                    case "result":
                        handleResult(json);
                        break;
                }
                break;
            default:
                log.warn("收到非法消息类型: {}", type);
                rejectSession(session, "非法操作类型: " + type);
        }
    }

    /**
     * ✅ 注册处理
     */
    private void handleRegister(WebSocketSession session, JSONObject json) {
        String role = json.getStr(HeartBeatConstant.ROLE);

        if (HeartBeatConstant.DEVICE.equals(role)) {
            String deviceId = json.getStr(HeartBeatConstant.DEVICE_ID);
            String processId = json.getStr(HeartBeatConstant.PROCESS_ID);
            if (StrUtil.isEmpty(deviceId) || StrUtil.isEmpty(processId)) {
                rejectSession(session, "设备注册缺少 device_id 或者 process_id 参数");
                return;
            }
            // 保存进程ID
            sessionRegistry.addProcess(processId, session);

            // 判断是否是主进程，如果是，保存主进程session，否则保存子进程的能力信息
            Boolean isMain = json.getBool(HeartBeatConstant.IS_MAIN);
            if (isMain) {
                // 保存主进程信息
                sessionRegistry.addDevice(deviceId, session);

                // 向主基座注册主进程信息
                try {
                    RegisterResponseDTO response = RegisterResponseDTO.builder()
                            .type(WebsocketTypeEnum.RESULT.getName())
                            .device_id(deviceId).process_id(processId)
                            .message(HeartBeatConstant.SUCCESS).build();
                    // 返回认证成功消息
                    session.sendMessage(new TextMessage(JSONUtil.toJsonStr(response)));

                    // 将消息转发到主机座，添加用户名、密码、TOKEN
                    json.put(HeartBeatConstant.USERNAME, websocketBaseUserName);
                    json.put(HeartBeatConstant.PASSWORD, websocketBasePassword);
                    json.put(HeartBeatConstant.TOKEN, "qmXA58CkVe3RyKvuMrHzH");

                    // 使用连接管理器发送消息
                    wsClientManager.createConnection(deviceId);
                    wsClientManager.sendMessage(deviceId, json.toString());
                } catch (IOException e) {
                    log.error("发送设备认证成功消息失败: {}", e.getMessage());
                }
                log.info("主进程注册成功，设备ID：{}", deviceId);
            } else {
                // 保存设备能力信息
                deviceService.saveDeviceCapability(json);
                log.info("子进程注册成功，GPU 编号：{}", json.getInt(HeartBeatConstant.GPU_INDEX));
            }
        } else {
            rejectSession(session, "未知角色类型：" + role);
        }
    }

    /**
     * ✅ 处理指令调用
     */
    private void handleInvoke(WebSocketSession session, JSONObject payload) {
        String capability = payload.getStr("capability");
        String requestId = payload.getStr("request_id");

        String targetDeviceId = deviceService.findAvailableDevice(capability);
        WebSocketSession deviceSession = sessionRegistry.getDeviceSession(targetDeviceId);

        if (targetDeviceId != null && deviceSession != null) {
            try {
                deviceSession.sendMessage(new TextMessage(payload.toString()));
            } catch (IOException e) {
                log.error("发送指令失败：{}", e.getMessage());
            }
        } else {
            log.warn("未找到可用设备，capability={}", capability);
            sendJson(session, Result.failed(ResultCode.RESOURCE_NOT_FOUND.getMessage()));
        }
    }

    private void handleResult(JSONObject json) {
        String requestId = json.getStr("request_id");
        log.info("接收到结果：requestId={}", requestId);
        log.info("结果内容：{}", json);
    }

    private void handleHeartbeat(WebSocketSession session, JSONObject json) {
        // 记录心跳信息
        deviceService.saveDeviceHeartbeat(json);

        try {
            // 返回记录成功
            session.sendMessage(new TextMessage(JSONUtil.toJsonStr(Result.success("设备心跳信息记录成功"))));

            // 转发消息到主基座
            wsDeviceClient.sendMessage(json.toString());
        } catch (IOException e) {
            log.error("设备心跳信息记录失败: {}", e.getMessage());
        }
    }

    private void rejectSession(WebSocketSession session, String reason) {
        JSONObject errorJson = new JSONObject();
        errorJson.set("type", "error");
        errorJson.set("message", reason);
        try (session) {
            try {
                session.sendMessage(new TextMessage(errorJson.toString()));
            } catch (IOException e) {
                log.warn("发送错误信息失败: {}", e.getMessage());
            }
        } catch (IOException e) {
            log.warn("关闭连接失败: {}", e.getMessage());
        }
    }

    private void sendJson(WebSocketSession session, Result<?> result) {
        try {
            session.sendMessage(new TextMessage(new JSONObject(result).toString()));
        } catch (IOException e) {
            log.warn("发送消息失败: {}", e.getMessage());
        }
    }
}
