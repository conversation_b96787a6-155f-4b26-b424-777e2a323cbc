package com.focus.iot.websocket;


import jakarta.annotation.PostConstruct;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2025/9/2
 * @desc Java作为客户端与另一个WebSocket服务器通信的实现类
 **/
@Component
public class WsDeviceClient extends WebSocketClient {

    @Value("${websocket.base.ip}")
    private String websocketBaseIp;

    @Value("${websocket.base.port}")
    private String websocketBasePort;

    /**
     * 构造函数，初始化WebSocket连接
     * 使用配置文件中的IP和端口构建WebSocket URI
     * */
    public WsDeviceClient() {
        super(createWebSocketURI());
    }

    /**
     * 根据配置文件中的IP和端口创建WebSocket URI
     * */
    private static URI createWebSocketURI() {
        // 这里使用默认值，实际的IP和端口会在Spring容器初始化后通过@PostConstruct方法重新连接
        try {
            return new URI("ws://localhost:8080/ws");
        } catch (Exception e) {
            throw new RuntimeException("创建WebSocket URI失败", e);
        }
    }

    /**
     * Spring容器初始化完成后，重新连接到正确的WebSocket服务器
     * */
    @PostConstruct
    public void initWebSocketConnection() {
        try {
            // 关闭默认连接
            if (this.isOpen()) {
                this.close();
            }

            // 使用配置的IP和端口重新创建URI并连接
            String wsUrl = String.format("ws://%s:%s/ws", websocketBaseIp, websocketBasePort);
            URI serverUri = new URI(wsUrl);
            this.uri = serverUri;

            // 重新连接
            this.connect();

        } catch (Exception e) {
            System.err.println("初始化WebSocket连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 当WebSocket连接成功建立时调用
     */
    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        System.out.println("WebSocket连接已建立！");
        System.out.println("服务器响应状态: " + serverHandshake.getHttpStatus() + " " + serverHandshake.getHttpStatusMessage());
        // 连接建立后可以立即发送一条消息
        send("客户端已连接，我是Java WebSocket客户端");
    }

    /**
     * 当收到服务器发送的消息时调用
     */
    @Override
    public void onMessage(String message) {
        System.out.println("\n收到服务器消息: " + message);
        System.out.println("请输入要发送的消息(输入exit断开连接):");
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("\n连接已关闭");
        System.out.println("关闭代码: " + code);
        System.out.println("关闭原因: " + reason);
    }

    @Override
    public void onError(Exception e) {
        System.err.println("发生错误: " + e.getMessage());
        e.printStackTrace();
    }
}
