package com.focus.iot.websocket;


import cn.hutool.json.JSONObject;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2025/9/2
 * @desc Java作为客户端与另一个WebSocket服务器通信的实现类
 **/
@Slf4j
@Component
public class WsDeviceClient extends WebSocketClient {

    @Value("${websocket.base.ip}")
    private String websocketBaseIp;

    @Value("${websocket.base.port}")
    private String websocketBasePort;

    /**
     * 构造函数，不立即连接，等待Spring注入配置后再连接
     * */
    public WsDeviceClient() {
        // 使用临时URI初始化父类，不会实际连接
        super(createTempURI());
    }

    /**
     * 创建临时URI用于初始化父类
     * */
    private static URI createTempURI() {
        try {
            return new URI("ws://temp:0/temp");
        } catch (Exception e) {
            throw new RuntimeException("创建临时URI失败", e);
        }
    }

    /**
     * Spring容器初始化完成后，使用正确的配置连接WebSocket服务器
     * */
    @PostConstruct
    public void initWebSocketConnection() {
        try {
            // 使用配置的IP和端口创建正确的URI
            String wsUrl = String.format("ws://%s:%s/ws", websocketBaseIp, websocketBasePort);
            this.uri = new URI(wsUrl);

            // 连接到WebSocket服务器
            this.connect();
            log.info("正在连接到WebSocket服务器: {}", wsUrl);
        } catch (Exception e) {
            log.error("初始化WebSocket连接失败: {}", e.getMessage());
        }
    }

    /**
     * 当WebSocket连接成功建立时调用
     */
    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        log.info("WebSocket连接已建立！");
        log.info("服务器响应状态: {}, {}", serverHandshake.getHttpStatus(),  serverHandshake.getHttpStatusMessage());
    }

    /**
     * 提供一个外部调用的发送方法
     * */
    public void sendMessage(String message) {
        if (isOpen()) { // 发送前检查连接是否打开
            send(message);
            //TODO 因为心跳太过频繁，测试完成后删除所有日志
            log.info("发送消息: {}", message);
        } else {
            log.error("连接未建立，无法发送消息");
        }
    }

    /**
     * 当收到服务器发送的消息时调用
     */
    @Override
    public void onMessage(String message) {
        log.info("收到服务器消息: {}", message);
        JSONObject json = new JSONObject(message);
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("\n连接已关闭");
        System.out.println("关闭代码: " + code);
        System.out.println("关闭原因: " + reason);
    }

    @Override
    public void onError(Exception e) {
        System.err.println("发生错误: " + e.getMessage());
        e.printStackTrace();
    }
}
