package com.focus.iot.websocket;


import org.springframework.beans.factory.annotation.Value;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2025/9/2
 * @desc Java作为客户端与另一个WebSocket服务器通信的实现类
 **/
@Component
public class WsDeviceClient extends WebSocketClient {

    @Value("${websocket.base.ip}")
    private String websocketBaseIp;

    @Value("${websocket.base.port}")
    private String websocketBasePort;

    /**
     * 构造函数，初始化WebSocket连接
     * */
    public WsDeviceClient(@Value("${websocket.base.ip:ws://localhost:8080/ws}") URI serverUri) {
        super(serverUri);
    }

    /**
     * 当WebSocket连接成功建立时调用
     */
    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        System.out.println("WebSocket连接已建立！");
        System.out.println("服务器响应状态: " + serverHandshake.getHttpStatus() + " " + serverHandshake.getHttpStatusMessage());
        // 连接建立后可以立即发送一条消息
        send("客户端已连接，我是Java WebSocket客户端");
    }

    /**
     * 当收到服务器发送的消息时调用
     */
    @Override
    public void onMessage(String message) {
        System.out.println("\n收到服务器消息: " + message);
        System.out.println("请输入要发送的消息(输入exit断开连接):");
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        System.out.println("\n连接已关闭");
        System.out.println("关闭代码: " + code);
        System.out.println("关闭原因: " + reason);
    }

    @Override
    public void onError(Exception e) {
        System.err.println("发生错误: " + e.getMessage());
        e.printStackTrace();
    }
}
