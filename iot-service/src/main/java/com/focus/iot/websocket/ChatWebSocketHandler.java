//package com.focus.iot.websocket;
//
//import org.springframework.web.socket.*;
//import org.springframework.web.socket.handler.TextWebSocketHandler;
//
//import java.util.Set;
//import java.util.concurrent.CopyOnWriteArraySet;
//
//public class ChatWebSocketHandler extends TextWebSocketHandler {
//
//    private final Set<WebSocketSession> sessions = new CopyOnWriteArraySet<>();
//
//    @Override
//    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
//        sessions.add(session);
//        session.sendMessage(new TextMessage("欢迎连接 WebSocket 服务！"));
//    }
//
//    @Override
//    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
//        for (WebSocketSession s : sessions) {
//            if (s.isOpen()) {
//                s.sendMessage(new TextMessage("用户[" + session.getId() + "]: " + message.getPayload()));
//            }
//        }
//    }
//
//    @Override
//    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
//        sessions.remove(session);
//    }
//}
