//package com.focus.iot.websocket;
//
//import jakarta.websocket.*;
//import jakarta.websocket.server.PathParam;
//import jakarta.websocket.server.ServerEndpoint;
//import lombok.extern.slf4j.Slf4j;
//
//import java.io.IOException;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
//@Slf4j
//@ServerEndpoint("/ws/chat/{userId}")
//public class ChatEndpoint {
//
//    private static final String ALLOWED_KEY = "SECRET-123456";
//    private static final Map<String, Session> userSessions = new ConcurrentHashMap<>();
//    private static final Map<String, Session> deviceSessions = new ConcurrentHashMap<>();
//
//    @OnOpen
//    public void onOpen(Session session, @PathParam("userId") String userId) {
//        String query = session.getQueryString(); // ?key=SECRET-123456
//        if (query == null || !query.contains("key=" + ALLOWED_KEY)) {
//            try {
//                session.close(new CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "密钥不正确"));
//                log.info("❌ 拒绝连接（密钥错误），用户：" + userId);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            return;
//        }
//
//        session.getUserProperties().put("userId", userId);
//
//        if (userId.startsWith("device_")) {
//            deviceSessions.put(userId, session);
//            log.info("📟 设备上线：" + userId);
//            notifyUsersDeviceOnline(userId);
//        } else if (userId.startsWith("user_")) {
//            userSessions.put(userId, session);
//            log.info("✅ 用户连接成功：" + userId);
//        } else {
//            log.info("⚠️ 未知类型连接：" + userId);
//        }
//    }
//
//    @OnMessage
//    public void onMessage(Session session, String message) throws IOException {
//        String senderId = (String) session.getUserProperties().get("userId");
//
//        log.info("📨 收到 [" + senderId + "] 的消息: " + message);
//
//        // 用户发送消息格式：SEND device_456 hello device!
//        if (senderId.startsWith("user_") && message.startsWith("SEND ")) {
//            String[] parts = message.split(" ", 3);
//            if (parts.length == 3) {
//                String targetDeviceId = parts[1];
//                String content = parts[2];
//                Session deviceSession = deviceSessions.get(targetDeviceId);
//                if (deviceSession != null && deviceSession.isOpen()) {
//                    deviceSession.getBasicRemote().sendText("来自用户 " + senderId + " 的消息: " + content);
//                    session.getBasicRemote().sendText("✅ 已发送到设备 " + targetDeviceId);
//                } else {
//                    session.getBasicRemote().sendText("❌ 设备 " + targetDeviceId + " 不在线");
//                }
//            } else {
//                session.getBasicRemote().sendText("❗ 格式错误，正确格式：SEND device_xxx 消息内容");
//            }
//        } else {
//            session.getBasicRemote().sendText("你说的是：" + message);
//        }
//    }
//
//    @OnClose
//    public void onClose(Session session) {
//        String userId = (String) session.getUserProperties().get("userId");
//        if (userId == null) return;
//
//        if (userId.startsWith("device_")) {
//            deviceSessions.remove(userId);
//            log.info("📴 设备离线：" + userId);
//        } else {
//            userSessions.remove(userId);
//            log.info("👋 用户断开连接：" + userId);
//        }
//    }
//
//    @OnError
//    public void onError(Session session, Throwable throwable) {
//        log.error("❗ WebSocket 错误：" + throwable.getMessage());
//    }
//
//    private void notifyUsersDeviceOnline(String deviceId) {
//        String notifyMsg = "📢 设备上线通知: " + deviceId;
//        for (Map.Entry<String, Session> entry : userSessions.entrySet()) {
//            try {
//                if (entry.getValue().isOpen()) {
//                    entry.getValue().getBasicRemote().sendText(notifyMsg);
//                }
//            } catch (IOException e) {
//                log.error("❗ 通知用户失败：" + entry.getKey());
//                e.printStackTrace();
//            }
//        }
//    }
//}
