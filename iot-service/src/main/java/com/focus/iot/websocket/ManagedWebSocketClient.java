package com.focus.iot.websocket;


import cn.hutool.json.JSONObject;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.registry.SessionRegistry;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.net.URI;

/**
 * <AUTHOR>
 * @date 2025/9/2
 * @desc Java作为客户端与另一个WebSocket服务器通信的实现类
 **/
@Slf4j
public class ManagedWebSocketClient extends WebSocketClient {

    private final String connectionId;
    private final SessionRegistry sessionRegistry;

    public ManagedWebSocketClient(String connectionId, URI serverUri, SessionRegistry sessionRegistry) {
        super(serverUri);
        this.connectionId = connectionId;
        this.sessionRegistry = sessionRegistry;
    }

    /**
     * 当收到服务器发送的消息时调用
     */
    @Override
    public void onMessage(String message) {
        log.info("收到来自连接 [{}] 的消息: {}", connectionId, message);
        try {
            handleIncomingMessage(connectionId, message);
        } catch (Exception e) {
            log.error("处理来自连接 [{}] 的消息失败: {}", connectionId, e.getMessage());
        }
    }
    
    /**
     * 当WebSocket连接成功建立时调用
     */
    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        log.info("WebSocket连接已建立: {} -> {}", connectionId, this.getURI());
        log.debug("服务器响应状态: {}, {}", serverHandshake.getHttpStatus(), serverHandshake.getHttpStatusMessage());
    }
    
    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("WebSocket连接已关闭: {}, 代码: {}, 原因: {}, 远程关闭: {}", 
            connectionId, code, reason, remote);
    }

    @Override
    public void onError(Exception e) {
        log.error("WebSocket连接发生错误: {}, 错误: {}", connectionId, e.getMessage());
    }
    
    /**
     * 处理接收到的消息
     *
     * @param connectionId 连接ID
     * @param message      消息内容
     */
    private void handleIncomingMessage(String connectionId, String message) {
        try {
            JSONObject json = new JSONObject(message);
            String type = json.getStr(HeartBeatConstant.TYPE);

            // 根据消息类型进行不同处理
            switch (type) {
                case "register":
                    handleRegisterMessage(connectionId, json);
                    break;
                case "config":
                    handleConfigMessage(connectionId, json);
                    break;
                case "control":
                    handleControlMessage(connectionId, json);
                    break;
                default:
                    log.warn("收到未知类型消息: {} from connection: {}", type, connectionId);
            }
        } catch (Exception e) {
            log.error("解析消息失败: {} from connection: {}", message, connectionId);
        }
    }

    /**
     * 处理注册结果消息
     */
    private void handleRegisterMessage(String connectionId, JSONObject json) {
        log.info("处理主基座返回的注册消息，来自连接 [{}] ，消息: {}", connectionId, json.toString());
    }

    /**
     * 处理子进程配置参数消息
     */
    private void handleConfigMessage(String connectionId, JSONObject json) {
        log.debug("处理子进程配置参数消息，连接 [{}] ，消息：{}", connectionId, json.toString());
        String deviceId = json.getStr(HeartBeatConstant.DEVICE_ID);
        WebSocketSession wsSession = sessionRegistry.getDeviceSession(deviceId);
        this.forwardMessageToSession(wsSession, json);
    }

    /**
     * 处理进程控制命令消息
     */
    private void handleControlMessage(String connectionId, JSONObject json) {
        log.info("处理进程控制命令消息，连接 [{}] ，消息: {}", connectionId, json.toString());
        log.info("收到主基座控制命令: {}", json);
        String deviceId = json.getJSONObject(HeartBeatConstant.PAYLOAD)
                .getStr(HeartBeatConstant.DEVICE_ID);
        WebSocketSession wsSession = sessionRegistry.getDeviceSession(deviceId);
        try {
            if (wsSession != null) {
                wsSession.sendMessage(new TextMessage(json.toString()));
            }
        } catch (IOException e) {
            log.error("发送设备进程控制消息失败: {}", e.getMessage());
        }
    }

    /**
     * 转发消息到WebSocket会话
     */
    private void forwardMessageToSession(WebSocketSession wsSession, JSONObject json) {
        try {
            if (wsSession != null) {
                wsSession.sendMessage(new TextMessage(json.toString()));
                log.info("向设备发送配置信息成功: {}", json);
            }
        } catch (IOException e) {
            log.error("发送设备进程控制消息失败: {}", e.getMessage());
        }
    }

    /**
     * 获取连接ID
     */
    public String getConnectionId() {
        return connectionId;
    }
}
