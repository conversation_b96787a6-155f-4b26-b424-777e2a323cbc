package com.focus.iot.websocket;

import com.focus.iot.registry.SessionRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/9/10
 * @desc WebSocket客户端连接管理器，管理多个WebSocket连接
 **/
@Slf4j
@Component
public class WsClientManager {

//    @Value("${websocket.base.ip}")
//    private String websocketBaseIp;

//    @Value("${websocket.base.port:63300}")
//    private String websocketBasePort;

    @Autowired
    private SessionRegistry sessionRegistry;

    // 存储所有的WebSocket客户端连接
    private final Map<String, ManagedWebSocketClient> clientConnections = new ConcurrentHashMap<>();

    /**
     * 创建新的WebSocket客户端连接（使用默认URL）
     * @param connectionId 连接唯一标识
     * @return 是否创建成功
     */
    public boolean createConnection(String connectionId) {
        return createConnection(connectionId, null);
    }

    /**
     * 创建新的WebSocket客户端连接
     * @param connectionId 连接唯一标识
     * @param serverUrl WebSocket服务器地址，如果为null则使用默认配置
     * @return 是否创建成功
     */
    public boolean createConnection(String connectionId, String serverUrl) {
        if (clientConnections.containsKey(connectionId)) {
            log.warn("连接ID已存在: {}", connectionId);
            return false;
        }

        try {
            String wsUrl = "wss://api.focus-jd.cn/ws";
//            String wsUrl = serverUrl != null ? serverUrl :
//                String.format("ws://%s:%s/ws", websocketBaseIp, websocketBasePort);

            URI serverUri = new URI(wsUrl);
            ManagedWebSocketClient client = new ManagedWebSocketClient(connectionId, serverUri, sessionRegistry);

            clientConnections.put(connectionId, client);
            client.connect();

            log.info("创建WebSocket连接成功: {} -> {}", connectionId, wsUrl);
            return true;
        } catch (Exception e) {
            log.error("创建WebSocket连接失败: {}, 错误: {}", connectionId, e.getMessage());
            return false;
        }
    }

    /**
     * 发送消息到指定连接
     * @param connectionId 连接ID
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendMessage(String connectionId, String message) {
        ManagedWebSocketClient client = clientConnections.get(connectionId);
        if (client == null) {
            log.warn("连接不存在: {}", connectionId);
            return false;
        }

        // 如果连接关闭，尝试连接一次
        if(client.isClosed()){
            client.connect();
        }

//        if (!client.isOpen()) {
//            log.warn("连接未打开: {}", connectionId);
//            return false;
//        }

        try {
            client.send(message);
            log.debug("发送消息成功: {} -> {}", connectionId, message);
            return true;
        } catch (Exception e) {
            log.error("发送消息失败: {}, 错误: {}", connectionId, e.getMessage());
            return false;
        }
    }

    /**
     * 关闭指定连接
     * @param connectionId 连接ID
     */
    public void closeConnection(String connectionId) {
        ManagedWebSocketClient client = clientConnections.remove(connectionId);
        if (client != null) {
            client.close();
            log.info("关闭WebSocket连接: {}", connectionId);
        }
    }

    /**
     * 获取连接状态
     * @param connectionId 连接ID
     * @return 连接是否打开
     */
    public boolean isConnectionOpen(String connectionId) {
        ManagedWebSocketClient client = clientConnections.get(connectionId);
        return client != null && client.isOpen();
    }

    /**
     * 获取所有连接ID
     * @return 连接ID集合
     */
    public java.util.Set<String> getAllConnectionIds() {
        return clientConnections.keySet();
    }

    /**
     * 关闭所有连接
     */
    public void closeAllConnections() {
        for (Map.Entry<String, ManagedWebSocketClient> entry : clientConnections.entrySet()) {
            entry.getValue().close();
            log.info("关闭WebSocket连接: {}", entry.getKey());
        }
        clientConnections.clear();
    }


}
