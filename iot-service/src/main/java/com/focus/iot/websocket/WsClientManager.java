package com.focus.iot.websocket;

import com.focus.iot.registry.SessionRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/9/10
 * @desc WebSocket客户端连接管理器，管理多个WebSocket连接
 **/
@Slf4j
@Component
public class WsClientManager {

//    @Value("${websocket.base.ip}")
//    private String websocketBaseIp;

//    @Value("${websocket.base.port:63300}")
//    private String websocketBasePort;

    @Autowired
    private SessionRegistry sessionRegistry;

    // 存储所有的WebSocket客户端连接
    private final Map<String, ManagedWebSocketClient> clientConnections = new ConcurrentHashMap<>();

    /**
     * 创建新的WebSocket客户端连接（使用默认URL）
     * @param connectionId 连接唯一标识
     * @return 是否创建成功
     */
    public boolean createConnection(String connectionId) {
        return createConnection(connectionId, null);
    }

    /**
     * 创建新的WebSocket客户端连接
     * @param connectionId 连接唯一标识
     * @param serverUrl WebSocket服务器地址，如果为null则使用默认配置
     * @return 是否创建成功
     */
    public boolean createConnection(String connectionId, String serverUrl) {
        if (clientConnections.containsKey(connectionId)) {
            log.warn("连接ID已存在: {}", connectionId);
            return false;
        }

        try {
            String wsUrl = "wss://api.focus-jd.cn/ws";
//            String wsUrl = serverUrl != null ? serverUrl :
//                String.format("ws://%s:%s/ws", websocketBaseIp, websocketBasePort);

            URI serverUri = new URI(wsUrl);
            ManagedWebSocketClient client = new ManagedWebSocketClient(connectionId, serverUri, sessionRegistry);

            clientConnections.put(connectionId, client);
            client.connect();

            log.info("创建WebSocket连接成功: {} -> {}", connectionId, wsUrl);
            return true;
        } catch (Exception e) {
            log.error("创建WebSocket连接失败: {}, 错误: {}", connectionId, e.getMessage());
            return false;
        }
    }

    /**
     * 发送消息到指定连接
     * @param connectionId 连接ID
     * @param message 消息内容
     * @return 是否发送成功
     */
    public boolean sendMessage(String connectionId, String message) {
        ManagedWebSocketClient client = clientConnections.get(connectionId);
        if (client == null) {
            log.warn("连接不存在: {}", connectionId);
            return false;
        }

        // 尝试发送消息，如果失败则重连后再试
        return sendMessageWithRetry(client, connectionId, message, 1);
    }

    /**
     * 带重试的消息发送
     */
    private boolean sendMessageWithRetry(ManagedWebSocketClient client, String connectionId, String message, int maxRetries) {
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // 检查连接状态
                if (!isClientReady(client)) {
                    log.info("连接 [{}] 状态异常 (attempt {}), 尝试重连...", connectionId, attempt + 1);
                    if (!reconnectClient(client, connectionId)) {
                        log.warn("连接 [{}] 重连失败 (attempt {})", connectionId, attempt + 1);
                        continue;
                    }
                }

                // 尝试发送消息
                client.send(message);
                log.debug("发送消息成功: {} -> {}", connectionId, message);
                return true;

            } catch (Exception e) {
                log.warn("发送消息失败 (attempt {}): {}, 错误: {}", attempt + 1, connectionId, e.getMessage());

                // 如果是最后一次尝试，记录错误并返回失败
                if (attempt == maxRetries) {
                    log.error("发送消息最终失败: {}, 已尝试 {} 次", connectionId, maxRetries + 1);
                    return false;
                }

                // 等待一段时间后重试
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 检查客户端是否准备就绪
     */
    private boolean isClientReady(ManagedWebSocketClient client) {
        if (client == null) {
            return false;
        }

        // 多重检查确保连接真正可用
        boolean isOpen = client.isOpen();
        boolean isClosed = client.isClosed();
        boolean isClosing = client.isClosing();

        log.debug("连接状态检查 [{}]: isOpen={}, isClosed={}, isClosing={}",
            client.getConnectionId(), isOpen, isClosed, isClosing);

        return isOpen && !isClosed && !isClosing;
    }

    /**
     * 重连客户端
     */
    private boolean reconnectClient(ManagedWebSocketClient client, String connectionId) {
        try {
            log.info("开始重连 [{}]...", connectionId);

            // 强制关闭现有连接
            if (!client.isClosed()) {
                client.close();
                // 等待连接完全关闭
                Thread.sleep(500);
            }

            // 重新连接
            client.reconnect();

            // 等待连接建立，最多等待5秒
            int waitTime = 0;
            while (!client.isOpen() && waitTime < 5000) {
                Thread.sleep(100);
                waitTime += 100;
            }

            boolean success = client.isOpen();
            if (success) {
                log.info("连接 [{}] 重连成功", connectionId);
            } else {
                log.warn("连接 [{}] 重连超时，等待了 {}ms", connectionId, waitTime);
            }

            return success;

        } catch (Exception e) {
            log.error("重连失败 [{}]: {}", connectionId, e.getMessage());
            return false;
        }
    }

    /**
     * 关闭指定连接
     * @param connectionId 连接ID
     */
    public void closeConnection(String connectionId) {
        ManagedWebSocketClient client = clientConnections.remove(connectionId);
        if (client != null) {
            client.close();
            log.info("关闭WebSocket连接: {}", connectionId);
        }
    }

    /**
     * 获取连接状态
     * @param connectionId 连接ID
     * @return 连接是否打开
     */
    public boolean isConnectionOpen(String connectionId) {
        ManagedWebSocketClient client = clientConnections.get(connectionId);
        return isClientReady(client);
    }

    /**
     * 获取所有连接ID
     * @return 连接ID集合
     */
    public java.util.Set<String> getAllConnectionIds() {
        return clientConnections.keySet();
    }

    /**
     * 关闭所有连接
     */
    public void closeAllConnections() {
        for (Map.Entry<String, ManagedWebSocketClient> entry : clientConnections.entrySet()) {
            entry.getValue().close();
            log.info("关闭WebSocket连接: {}", entry.getKey());
        }
        clientConnections.clear();
    }

    /**
     * 获取连接详细状态信息（用于调试）
     */
    public String getConnectionStatus(String connectionId) {
        ManagedWebSocketClient client = clientConnections.get(connectionId);
        if (client == null) {
            return "连接不存在";
        }

        return String.format("连接 [%s] 状态: isOpen=%s, isClosed=%s, isClosing=%s, URI=%s",
            connectionId,
            client.isOpen(),
            client.isClosed(),
            client.isClosing(),
            client.getURI());
    }

    /**
     * 获取所有连接的状态信息
     */
    public void logAllConnectionStatus() {
        log.info("=== 所有连接状态 ===");
        for (String connectionId : clientConnections.keySet()) {
            log.info(getConnectionStatus(connectionId));
        }
        log.info("=== 状态检查完成 ===");
    }


}
