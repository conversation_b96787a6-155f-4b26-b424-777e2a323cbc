//package com.focus.iot.config;
//
//import com.focus.iot.websocket.ChatEndpoint;
//import jakarta.annotation.PostConstruct;
//import jakarta.annotation.Resource;
//import jakarta.servlet.ServletContext;
//import jakarta.websocket.server.ServerContainer;
//import org.springframework.context.annotation.Configuration;
//
//
//@Configuration
//public class WebSocketConfig {
//
//    @Resource
//    private ServletContext servletContext;
//
//    @PostConstruct
//    public void registerWebSocketEndpoint() {
//        try {
//            ServerContainer serverContainer = (ServerContainer)
//                    servletContext.getAttribute("jakarta.websocket.server.ServerContainer");
//
//            serverContainer.addEndpoint(ChatEndpoint.class);
//        } catch (Exception e) {
//            throw new RuntimeException("WebSocket endpoint 注册失败", e);
//        }
//    }
//}
