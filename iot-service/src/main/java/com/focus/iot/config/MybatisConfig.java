package com.focus.iot.config;

import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class MybatisConfig {
//    /**
//     * 分页插件
//     *
//     * @return
//     */
//    @Bean
//    public MybatisPlusInterceptor mybatisPlusInterceptor() {
//        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
//        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
//        return interceptor;
//    }

}
