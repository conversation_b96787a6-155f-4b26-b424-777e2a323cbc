package com.focus.iot.config;

import com.baomidou.mybatisplus.extension.ddl.IDdl;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.function.Consumer;

@Component
public class MyInitTableDdl implements IDdl {
    public void execute() {
        System.out.println("执行建表语句或初始化 SQL");
    }

    @Override
    public void runScript(Consumer<DataSource> consumer) {
        System.out.println(1); // 这里可实现自定义脚本逻辑
    }

    @Override
    public List<String> getSqlFiles() {
        return List.of(); // 这里你可以返回 SQL 脚本文件路径列表
    }
}
