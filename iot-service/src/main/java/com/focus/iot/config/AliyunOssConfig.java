package com.focus.iot.config;

import com.aliyun.oss.OSSClient;
import com.focus.iot.dao.service.IConfigService;
import jakarta.annotation.Resource;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云 OSS 基本配置
 */
@Configuration
@ConfigurationProperties(prefix = "oss")
@Data
public class AliyunOssConfig {
    @Value("${oss.file.endpoint}")
    private String endpoint;
    @Value("${oss.file.bucket}")
    private String bucketName;
    @Value("${oss.file.host}")
    private String ossHost;
    @Value("${oss.file.port}")
    private String ossPort;
    @Resource
    private IConfigService iConfigService;

//    @Bean
//    public OSSClient OSSClient() {
//        return new OSSClient(endpoint, iConfigService.getTextConfig("oss", "accessKeyId"), iConfigService.getTextConfig("oss", "accessKeySecret"));
//    }

}
