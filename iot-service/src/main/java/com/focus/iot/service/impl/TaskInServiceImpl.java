package com.focus.iot.service.impl;

import com.focus.iot.enums.ServiceType;
import com.focus.iot.service.ITaskInService;
import com.focus.iot.vo.RunningTask;
import com.focus.iot.vo.TaskStatusResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskInServiceImpl implements ITaskInService {

    private final StringRedisTemplate redisTemplate;

    public TaskStatusResponse getAllTaskStatus() {
        // 1. 获取所有 active_ports:* 键，提取 IP
        Set<String> activePortKeys = scanKeys("active_ports:*");
        Pattern ipPattern = Pattern.compile("active_ports:(.+)");
        Set<String> allIps = new HashSet<>();

        for (String key : activePortKeys) {
            Matcher matcher = ipPattern.matcher(key);
            if (matcher.matches()) {
                allIps.add(matcher.group(1));
            }
        }

        // 2. 获取正在运行的任务
        List<RunningTask> runningTasks = new ArrayList<>();
        for (String ip : allIps) {
            List<String> ports = redisTemplate.opsForList().range("active_ports:" + ip, 0, -1);
            if (ports == null) continue;

            for (String port : ports) {
                String runningKey = "running:" + ip + ":" + port;
                Boolean isRunning = redisTemplate.hasKey(runningKey);
                if (Boolean.TRUE.equals(isRunning)) {
                    String taskId = redisTemplate.opsForValue().get("task_id:" + ip + ":" + port);
                    if (taskId != null) {
                        runningTasks.add(new RunningTask(ip, port, taskId));
                    }
                }
            }
        }

        // 3. 获取全局队列任务列表
        List<String> globalQueue = redisTemplate.opsForList().range("task_queue:global", 0, -1);
        if (globalQueue == null) globalQueue = List.of();

        return new TaskStatusResponse(globalQueue, runningTasks);
    }

    // Redis scan 工具函数
    private Set<String> scanKeys(String pattern) {
        Set<String> keys = new HashSet<>();
        ScanOptions options = ScanOptions.scanOptions().match(pattern).count(1000).build();
        try (Cursor<byte[]> cursor = redisTemplate.getConnectionFactory().getConnection().scan(options)) {
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
        }
        return keys;
    }


    @Override
    public Map<String, Object> queryActivePorts(String ip) {
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("ip", ip);

        List<Map<String, Object>> services = new ArrayList<>();
        int totalPorts = 0;

        for (ServiceType serviceType : ServiceType.values()) {
            String redisKey = String.format("active_ports:%s:%s", ip, serviceType.name());
            String countKey = String.format("active_ports_count:%s:%s", ip, serviceType.name());

            List<String> ports = redisTemplate.opsForList().range(redisKey, 0, -1);
            String count = redisTemplate.opsForValue().get(countKey);

            Map<String, Object> serviceInfo = new HashMap<>();
            serviceInfo.put("serviceType", serviceType.name());
            serviceInfo.put("serviceName", serviceType.name());
            serviceInfo.put("portCount", count == null ? 0 : Integer.parseInt(count));
            serviceInfo.put("ports", ports == null ? Collections.emptyList() : ports);

            services.add(serviceInfo);
            totalPorts += ports == null ? 0 : ports.size();
        }

        result.put("totalPorts", totalPorts);
        result.put("services", services);
        return result;
    }

    @Override
    public List<Map<String, Object>> queryAllActiveHosts() {
        Set<String> ipList = redisTemplate.opsForSet().members("active_ip_list");
        if (ipList == null || ipList.isEmpty()) {
            return Collections.emptyList();
        }

        return ipList.stream()
                .map(this::queryActivePorts)
                .collect(Collectors.toList());
    }
}
