package com.focus.iot.service;

/**
 * <AUTHOR>
 * @ClassName CommonBizService
 * @description: TODO
 * @date 2024/04/01
 * @version: 1.0
 */
public interface ICommonBizService {
    /**
     * 生成位订单编号
     *
     * @param orderPrefix
     * @param format
     * @param length
     * @return
     */
    String generateNumber(String orderPrefix, String format, Integer length);

    /**
     *	统计是否超限
     * @param key
     * @param time
     * @param num
     * @return
     */
    boolean count(String key, long time, int num);
    /**
     *	统计是否超限
     * @param key
     * @param time
     * @param num
     * @return
     */
    boolean counts(String key, long time, int num);
}
