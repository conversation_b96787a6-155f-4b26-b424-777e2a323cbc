package com.focus.iot.service;

import cn.hutool.json.JSONObject;

public interface IDeviceInfoBizService {

    /**
     * 查找具有指定能力的可用设备
     * @param capability 设备能力标识
     * @return 设备ID，如果没有可用设备则返回null
     */
    String findAvailableDevice(String capability);

    /**
     * 存储设备心跳信息
     * */
    void saveDeviceHeartbeat(JSONObject json);

    /**
     * 保存注册能力信息
     * */
    void saveDeviceCapability(JSONObject json);

}
