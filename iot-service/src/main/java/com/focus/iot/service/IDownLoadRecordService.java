package com.focus.iot.service;

import com.focus.iot.dto.DownloadTask;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface IDownLoadRecordService {
    /**
     * 记录下载成功
     *
     * @param task     下载任务
     * @param fileSize 文件大小
     * @param speedMBps 下载速度（单位：MB/s）
     * @param hash     文件哈希值
     */
    void recordDownloadSuccess(DownloadTask task, BigDecimal fileSize, BigDecimal speedMBps);


    /**
     * 记录下载失败
     *
     * @param task     下载任务
     * @param errorMsg 错误信息
     * @param createTime 创建时间
     */
    void recordDownloadFailure(DownloadTask task, String errorMsg, LocalDateTime createTime);
}
