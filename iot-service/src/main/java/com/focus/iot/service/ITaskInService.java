package com.focus.iot.service;

import com.focus.iot.vo.TaskStatusResponse;

import java.util.List;
import java.util.Map;

public interface ITaskInService {

    /**
     * 获取所有任务状态
     *
     * @return TaskStatusResponse 包含所有任务的状态信息
     */
    TaskStatusResponse getAllTaskStatus();



    // 新增查询方法
    Map<String, Object> queryActivePorts(String ip);
    List<Map<String, Object>> queryAllActiveHosts();
}
