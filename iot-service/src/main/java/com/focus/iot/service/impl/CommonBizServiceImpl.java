package com.focus.iot.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.focus.iot.constant.RedisConstant;
import com.focus.iot.dao.service.IConfigService;
import com.focus.iot.service.ICommonBizService;
import com.focus.iot.service.IRedisBizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName CommonBizServiceImpl
 * @description: TODO
 * @date 2024/04/01
 * @version: 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonBizServiceImpl implements ICommonBizService {
    private final IConfigService configService;
    private final IRedisBizService redisService;
    @Value("${oss.file.endpoint}")
    private String endpoint;

    @Value("${oss.file.bucket}")
    private String bucketName;

    @Value("${oss.file.urlPrefix}")
    private String urlPrefix;

    @Value("${oss.file.webHost}")
    private String webHost;


    @Override
    public String generateNumber(String orderPrefix, String format, Integer length) {
        String dateStr = DateUtil.format(DateTime.now(), format);
        long num = redisService.incr(orderPrefix + ":" + dateStr, 1L);
        redisService.expire(orderPrefix + ":" + dateStr, 60 * 60 * 24);
        String orderNum = orderPrefix + dateStr;
        log.info("订单编号：{},编号：{},生成结果：{},长度：{}，补充长度:{}", orderNum, num, orderNum + StrUtil.padPre(StrUtil.toString(num), length - StrUtil.length(orderNum), "0"), length, length - StrUtil.length(orderNum + num));
        return orderNum + StrUtil.padPre(StrUtil.toString(num), length - StrUtil.length(orderNum), "0");
    }


    /**
     * 统计是否超限
     *
     * @param key
     * @param time
     * @param num
     * @return
     */
    @Override
    public boolean count(String key, long time, int num) {
        key = RedisConstant.KEY_PREFIX_VALUE + key;
        // 查询
        long increment = redisService.incr(key, 1L);
        if (increment <= num) {
            if (redisService.getExpire(key) == -1) {
                redisService.expire(key, time);
            }
            return true;
        }
        return false;
    }

    /**
     * 统计是否超限2
     *
     * @param key
     * @param time
     * @param num
     * @return
     */
    @Override
    public boolean counts(String key, long time, int num) {
        key = RedisConstant.KEY_PREFIX_VALUE + key;
        // 查询
        long increment = redisService.incr(key, 1L);
        if (increment <= num) {
            if (redisService.getExpire(key) == -1) {
                redisService.expire(key, time);
            }
            return true;
        }
        redisService.decr(key, 1L);
        return false;
    }


}
