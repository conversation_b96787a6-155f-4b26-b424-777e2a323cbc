package com.focus.iot.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.focus.iot.dao.domain.FileBase;
import com.focus.iot.dao.domain.FileProcessLog;
import com.focus.iot.dao.domain.FileSourceUrl;
import com.focus.iot.dao.service.IFileBaseService;
import com.focus.iot.dao.service.IFileProcessLogService;
import com.focus.iot.dao.service.IFileSourceUrlService;
import com.focus.iot.dto.DownloadTask;
import com.focus.iot.service.IDownLoadRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class DownloadRecordService implements IDownLoadRecordService {

    private final IFileBaseService fileBaseService;
    private final IFileSourceUrlService fileSourceUrlService;
    private final IFileProcessLogService fileProcessLogService;

    /**
     * 记录下载成功
     * @param task 下载任务
     * @param fileSize 文件大小
     * @param speedMBps 下载速度（单位：MB/s）
     * @param hash 文件哈希值
     */
    public void recordDownloadSuccess(DownloadTask task, BigDecimal fileSize, BigDecimal speedMBps) {
        String fileCode = task.getTaskId();
        String fileName = Path.of(task.getTargetPath()).getFileName().toString();
        String fileType = getFileExtension(fileName);


        // 插入 t_file_base
        FileBase base = new FileBase();
        base.setFileCode(fileCode);
        base.setFileName(fileName);
        base.setFileType(fileType);
        base.setSize(fileSize);
        base.setLocalPath(task.getTargetPath());
        base.setDownloadStatus("success");

        base.setCreateTime(LocalDateTime.now());
        base.setUpdateTime(LocalDateTime.now());
        fileBaseService.saveFile(base);

        // 插入 t_file_source_url
        FileSourceUrl url = new FileSourceUrl();
        url.setFileCode(fileCode);
        url.setSourceUrl(task.getFileUrl());
        url.setTaskId(fileCode);
        url.setRetryCount(task.getRetryCount());
        url.setDownloadStatus("success");
        url.setLastDownloadTime(LocalDateTime.now());
        url.setCreateTime(LocalDateTime.now());
        fileSourceUrlService.save(url);

        // 插入日志
        FileProcessLog log = new FileProcessLog();
        log.setFileCode(fileCode);
        log.setStep(1);
        log.setStatus(1);
        log.setMessage("下载成功，文件大小：" + fileSize + " M，速度：" + NumberUtil.round(speedMBps, 2) + " MB/s");
        log.setCreateTime(LocalDateTime.now());
        fileProcessLogService.save(log);
    }

    /**
     * 记录下载失败
     * @param task 下载任务
     * @param errorMsg 错误信息
     * @param createTime 创建时间
     */
    public void recordDownloadFailure(DownloadTask task, String errorMsg, LocalDateTime createTime) {
        fileBaseService.updateStatus(task.getTaskId(), "failed", errorMsg, createTime);

        FileProcessLog log = new FileProcessLog();
        log.setFileCode(task.getTaskId());
        log.setStep(1);
        log.setStatus(2);
        log.setMessage("下载失败，错误信息：" + errorMsg);
        log.setCreateTime(LocalDateTime.now());
        fileProcessLogService.save(log);
    }

    private String getFileExtension(String filename) {
        int idx = filename.lastIndexOf('.');
        return (idx >= 0) ? filename.substring(idx + 1) : "bin";
    }
}
