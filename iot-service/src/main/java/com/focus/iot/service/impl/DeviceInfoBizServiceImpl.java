package com.focus.iot.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.constant.RedisConstant;
import com.focus.iot.dao.domain.DeviceCapability;
import com.focus.iot.dao.service.*;
import com.focus.iot.service.IDeviceInfoBizService;
import com.focus.iot.service.IRedisBizService;
import com.focus.iot.vo.GpuInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Service
public class DeviceInfoBizServiceImpl implements IDeviceInfoBizService {

    @Autowired
    private IDeviceGpuStatusService gpuStatusService;

    @Autowired
    private IDeviceGpuStatusHistoryService gpuStatusHistoryService;

    @Autowired
    private IDeviceStatusService deviceStatusService;

    @Autowired
    private IDeviceStatusHistoryService deviceStatusHistoryService;

    @Autowired
    private IDeviceProcessStatusService deviceProcessStatusService;

    @Autowired
    private IDeviceProcessStatusHistoryService deviceProcessStatusHistoryService;

    @Autowired
    private IDeviceInfoService deviceInfoService;

    @Autowired
    private IRedisBizService redisBizService;

    @Autowired
    private IDeviceCapabilityService deviceCapabilityService;


    @Override
    public String findAvailableDevice(String capability) {
        List<DeviceCapability> candidates = deviceCapabilityService.list(Wrappers.<DeviceCapability>lambdaQuery().eq(DeviceCapability::getCapabilityType, capability));
        /**
         * todo 这里可以增加更多的逻辑来选择最合适的设备，比如根据负载、响应时间等指标进行排序和筛选
         */
        if (candidates == null || candidates.isEmpty()) {
            log.warn("未找到符合条件的设备，capability: {}", capability);
            return null;
        }
        return candidates.getFirst().getDeviceId();
    }

    @Override
    public void saveDeviceHeartbeat(JSONObject json) {
        JSONObject payload = json.getJSONObject(HeartBeatConstant.PAYLOAD);

        // 1、更新GPU设备信息实时状态
        gpuStatusService.updateGpuStatusInfo(payload);
        // 2、记录GPU设备信息
        List<GpuInfoVO> gpuInfoVOList = gpuStatusHistoryService.saveGpuStatusInfo(payload);

        // 计算GPU平均利用率，已是百分比
        BigDecimal sumGpuUti = gpuInfoVOList.stream().map(GpuInfoVO::getUtilization).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal avgGpuUtil = sumGpuUti.divide(BigDecimal.valueOf(gpuInfoVOList.size()), 2, BigDecimal.ROUND_HALF_UP);
        // 计算GPU显存使用率
        int sumGpuMemTotal = gpuInfoVOList.stream().mapToInt(GpuInfoVO::getMemoryTotal).sum();
        int sumGpuMemUsed = gpuInfoVOList.stream().mapToInt(GpuInfoVO::getMemoryUsed).sum();
        BigDecimal gpuMemAvg = BigDecimal.valueOf(sumGpuMemUsed).divide(BigDecimal.valueOf(sumGpuMemTotal), 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));

        // 3、更新设备信息实时状态
        deviceStatusService.updateDeviceStatusInfo(payload, avgGpuUtil, gpuMemAvg);
        // 4、记录设备信息
        deviceStatusHistoryService.saveDeviceStatusInfo(payload, avgGpuUtil);
        // 5、首次发送的设备需要保存设备信息
        boolean isExist = redisBizService.containsSetValue(RedisConstant.KEY_HEARTBEAT_DEVICE_ID, payload.getStr(HeartBeatConstant.DEVICE_ID));
        if (!isExist) {
            redisBizService.cacheSet(RedisConstant.KEY_HEARTBEAT_DEVICE_ID, payload.getStr(HeartBeatConstant.DEVICE_ID));
            deviceInfoService.saveDeviceInfo(payload, gpuInfoVOList.size());
        }

        // 6、更新进程信息实时状态
        deviceProcessStatusService.updateProcessStatusInfo(payload);
        // 7、记录进程信息实时状态
        deviceProcessStatusHistoryService.saveProcessStatusInfo(payload);

    }

    @Override
    public void saveDeviceCapability(JSONObject json) {
        deviceCapabilityService.saveDeviceCapability(json);
    }
}
