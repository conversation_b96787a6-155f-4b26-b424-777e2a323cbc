package com.focus.iot.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/9/3
 * @desc
 **/
@Data
@Builder
public class ProcessPayloadDTO {
    /**
     * 请求ID
     */
    private String request_id;

    /**
     * 进程ID
     */
    private String process_id;

    /**
     * 操作：start启动进程；stop停止进程；kill关闭进程；restart重启进程；
     */
    private String action;

    /**
     * GPU编号
     */
    private Integer gpu_index;

}
