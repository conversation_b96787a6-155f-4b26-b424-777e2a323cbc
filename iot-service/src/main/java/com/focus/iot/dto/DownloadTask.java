package com.focus.iot.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 表示一个文件下载任务的数据传输对象（DTO）。
 * 该类用于封装下载任务的基本信息，如任务ID、下载地址、目标路径等。
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DownloadTask implements Serializable {

    // 下载任务的唯一标识符
    private String taskId;

    /**
     * 文件类型
     */
    private String fileType;

    // 需要下载的文件的URL地址
    private String fileUrl;

    // 下载后的文件保存路径
    private String targetPath;

    // 下载失败时的最大重试次数，默认为3次
    private int retryCount = 3;

    // 下载任务的并发线程数，默认为6，用于提高下载速度
    private int threadCount = 6;
}
