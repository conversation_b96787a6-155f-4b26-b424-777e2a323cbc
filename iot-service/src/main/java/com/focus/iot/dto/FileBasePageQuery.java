package com.focus.iot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileBasePageQuery {
    /**
     * 分页查询页码
     * 注意：如果不传，则默认查询第一页数据
     */
    private int page = 1;
    /**
     * 分页查询页大小
     * 注意：如果不传，则默认查询10条数据
     */
    private int size = 10;
    /**
     * 分页查询开始时间
     * 注意：如果不传，则默认查询当前时间之前的所有数据
     */
    private LocalDateTime startTime;
    /**
     * 分页查询结束时间
     * 注意：如果不传，则默认查询当前时间之前的所有数据
     */
    private LocalDateTime endTime;


    /**
     * 对外文件编号
     */
    private String fileCode;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件类型(扩展名)
     */
    private String fileType;
}
