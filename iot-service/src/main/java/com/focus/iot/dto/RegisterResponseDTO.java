package com.focus.iot.dto;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/8/29
 * @Desc
 **/
@Data
@Builder
public class RegisterResponseDTO {

    /**
     * 返回信息类型
     */
    private String type;

    /**
     * 设备ID
     */
    private String device_id;

    /**
     * 进程ID
     */
    private String process_id;

    /**
     * 返回信息：成功返回success
     */
    private String message;

    /**
     * 设备所属区域
     */
    private String region;

}

