package com.focus.iot.constant;

/**
 * <AUTHOR>
 * @Date 2025/8/26
 * @Desc
 **/
public class HeartBeatConstant {

        // 类型
    public static final String USERNAME = "user_name";
        // 类型
    public static final String PASSWORD = "password";

    // 类型
    public static final String TYPE = "type";
    // 角色
    public static final String ROLE = "role";
    // 设备
    public static final String DEVICE = "device";
    // 区域
    public static final String REGION = "region";
    // 成功
    public static final String SUCCESS = "success";
    // 能力
    public static final String CAPABILITIES = "capabilities";
    // 是否是主进程
    public static final String IS_MAIN = "is_main";


    // 设备信息
    public static final String PAYLOAD = "payload";

    //设备ID
    public static final String DEVICE_ID = "device_id";
    //进程ID
    public static final String PROCESS_ID = "process_id";
    //主机名称
    public static final String HOSTNAME = "hostname";
    // 0-在线，1-离线，2-异常
    public static final String STATUS = "status";
    //cpu使用率
    public static final String CPU_USAGE = "cpu_usage";
    //内存使用率
    public static final String MEMORY_USAGE = "memory_usage";
    //磁盘使用率
    public static final String DISK_USAGE = "disk_usage";
    //网络入带宽
    public static final String NETWORK_IN = "network_in";
    //网络出带宽
    public static final String NETWORK_OUT = "network_out";
    //温度
    public static final String TEMPERATURE = "temperature";
    //时间戳
    public static final String TIMESTAMP = "timestamp";

    // GPU 信息
    // GPU
    public static final String GPU = "gpu";
    //GPUId
    public static final String GPU_INDEX = "gpu_index";
    //GPU名称
    public static final String GPU_NAME = "gpu_name";
    //GPU 使用率（%）
    public static final String UTILIZATION = "utilization";
    //总内存
    public static final String MEMORY_TOTAL = "memory_total";
    //内存使用量
    public static final String MEMORY_USED = "memory_used";
    //功耗（W）
    public static final String POWER_DRAW = "power_draw";

    // 进程信息
    public static final String PROCESS = "process";
    public static final String PROCESS_TYPE = "process_type";
    public static final String PROCESS_STATUS = "process_status";



}