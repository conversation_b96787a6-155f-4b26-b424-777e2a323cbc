package com.focus.iot.constant;

/**
 * <AUTHOR>
 * @ClassName UrlConstant
 * @description: TODO
 * @date 2024/03/16
 * @version: 1.0
 */
public class UrlConstant {
    public static final String GET_CREATIONS = "https://hiflyworks-api.lingverse.co/api/app/v1/creations";


    public static final String CREATIONS_URL = "https://hfw-api.lingverse.co/api/v1/hifly/task/create";

    public static final String GET_CREATIONS_API = "https://hfw-api.lingverse.co/api/v1/hifly/task/inspect";

    public static final String GET_AUDIO_INFO = "apiPai/video/getMesByUrl?url=";

    public static final String SNAPSHOT = "?x-oss-process=video/snapshot,t_5000,m_fast,ar_auto";


    /**
     * 创建音色ID
     */
    public static final String GET_VOICE_ID = "https://hfw-api.lingverse.co/api/v1/hifly/voice/create";

    /**
     * 声音克隆
     */
    public static final String VOICE_CLONE = "https://hfw-api.lingverse.co/api/v1/hifly/voice/train";

    /**
     * 声音确认
     */
    public static final String VOICE_CONFIRM = "https://hfw-api.lingverse.co/api/v1/hifly/voice/confirm";

    /**
     * 声音复刻API-2.0
     */
    public static final String SPEECH_CLONE = "https://openspeech.bytedance.com/api/v1/mega_tts/audio/upload";
    /**
     * 语音合成
     */
    public static final String SPEECH_SYNTHESIS = "https://openspeech.bytedance.com/api/v1/tts";


    public static final String CO_ZE_URL = "https://api.coze.cn/v3/chat";

    /**
     * 文本生成图像
     */
    public static final String IMAGE_SYNTHESIS = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis";

    /**
     * 查询文本生成图像任务
     */
    public static final String IMAGE_SYNTHESIS_TASKS= "https://dashscope.aliyuncs.com/api/v1/tasks/";
}
