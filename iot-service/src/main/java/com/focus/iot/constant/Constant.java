package com.focus.iot.constant;

/**
 *
 */
public class Constant {

    /**
     * mq
     */
    public static final String MP4_T0_MP3 = "mp4ToMp3";

    /**
     * 爬取视频mq
     */
    public static final String nutch = "nutch";

    /**
     * 获取抖音视频前缀
     */
    public static final String DOUYIN_VIDEO_PREFIX = "https://www.douyin.com/video/";

    /**
     * 视频提取文案状态码错误分析
     */
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE1 = "提交任务后，超过50s没有返回任务结果，服务端会返回此错误信息。";
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE2 = "请求数量过多";
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE3 = "不支持的音频格式";
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE4 = "无效的音频";
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE5 = "音频解码错误";
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE6 = "无有效音频流";
    public static final String WITHDRAW_FAIL_REASONSTATUS_CODE7 = "文件下载失败";
    /**
     * 获取音频信息
     */
    public static final String add_Ai_Speech_File = "apiPai/video/getMesByUrl?url=";
    /**
     * 根据音频文件url转化为mp3
     */
    public static final String CONVERT_MP3 = "apiPai/video/audit/convertMp3";

    public static final String SPLICE_URL = "https://oss.pai-id.com/";

    public static final String API_PAI = "apiPai/video/getMesByUrl?url=";
    public static final String SZR_UPLOAD = "https://szr-upload.oss-cn-hangzhou.aliyuncs.com/";

    /**
     * 视频提取状态(0-未提取)
     */
    public static final Integer NO_WITHDRAW = 0;

    /**
     * 视频提取状态(1-提取中)
     */
    public static final Integer EXTRACTING = 1;

    /**
     * 视频提取状态(2-提取成功
     */
    public static final Integer SUCCESSFUL_EXTRACTION = 2;
    /**
     * 视频提取状态(3-提取失败)
     */
    public static final Integer FAILURE_FETCH = 3;

    /**
     * 视频分析状态(0-未分析)
     */
    public static final Integer NOT_ANALYZED = 0;
    /**
     * 视频分析状态(1-分析中)
     */
    public static final Integer IN_THE_ANALYSIS = 1;
    /**
     * 视频分析状态(2-分析成功)
     */
    public static final Integer ANALYSIS_SUCCESS = 2;
    /**
     * 视频分析状态(3-分析失败)
     */
    public static final Integer ANALYSIS_FAILURE = 3;
    /**
     * 手动编辑
     */
    public static final Integer MANUAL_EDITING = 0;
    /**
     * ai编辑
     */
    public static final Integer AI_OPTIMIZATION = 1;
    /**
     * 未编辑
     */
    public static final Integer NOT_EDITED = 0;
    /**
     * 已编辑
     */
    public static final Integer HAVE_BEEN_EDITED = 1;

    public static final String SH_REGION_ID = "cn-shanghai";
    public static final String SH_END_POINT = "green-cip.cn-shanghai.aliyuncs.com";
    public static final String TEXT_SERVICE = "chat_detection";
    public static final String VOICE_SERVICE = "audio_media_detection";
    public static final String BJ_REGION_ID = "cn-beijing";
    public static final String BJ_END_POINT = "green-cip.cn-beijing.aliyuncs.com";
    public static final String VIDEO_SERVICE = "videoDetection";
    public static final String REQUEST_SERVICE = "baselineCheck";
    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;
    public static final Integer TWO = 2;
    public static final Integer THREE = 3;
    public static final Integer FOUR = 4;

    public static final String SITTING_POSITION = "（坐姿）";
    public static final String STANDING_POSTURE = "（站姿）";

    public static final Integer VERSION_NUMBER1 = 1;
    public static final Integer VERSION_NUMBER2 = 2;
    public static final Integer VERSION_NUMBER3 = 3;
    public static final Integer VERSION_NUMBER4 = 4;
    public static final Integer VERSION_NUMBER5 = 5;

    public static final String USER_MENU = "combo";
    public static final String HI_FLY = "hifly";
    public static final String RECHARGE = "recharge";

    public static final String DOMAIN = "nls-meta.cn-shanghai.aliyuncs.com";
    // API版本
    public static final String API_VERSION = "2019-02-28";
    // API名称
    public static final String REQUEST_ACTION = "CreateToken";
    // 响应参数
    public static final String KEY_TOKEN = "Token";
    public static final String KEY_ID = "Id";
    public static final String KEY_EXPIRE_TIME = "ExpireTime";

    public static final String OSS_TYPE = "oss";

    public static final String ACCESS_KEY_ID = "accessKeyId";

    public static final String ACCESS_KEY_SECRET = "accessKeySecret";

    /**
     * 豆包语音技术appId
     */
    public static final String DOU_BAO = "doubao";
    public static final String DOU_BAO_APPID = "appId";
    public static final String DOU_BAO_TOKEN = "token";

    /**
     * 在线支付
     */
    public static final String ZX = "zx";

    public static final String BASE_BROKEN = "base_broken";


    public static final String HI_FLY_MENU = "hiFlyMenu";

    /**
     * 声音克隆
     */
    public static final String CLONE_FAST = "CF";
    /**
     * 声音合成
     */
    public static final String synthesize = "SS";

    /**
     * 商务版数字人训练
     */
    public static final String transDig="TD";
    /**
     * 商务版数字人合成
     */
    public static final String creation="CC";
    /**
     * 精英版数字人训练
     */
    public static final String eliteTransDig="ET";
    /**
     * 精英版数字人合成
     */
    public static final String eliteCreation="EC";
}
