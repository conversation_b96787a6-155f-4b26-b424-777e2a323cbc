package com.focus.iot.constant;

/**
 * redis配置
 *
 * <AUTHOR>
 * @date 2023/7/28 14:03
 */
public class RedisConstant {

    /**
     * 心跳信息中的设备信息
     */
    public static final String KEY_HEARTBEAT_DEVICE_ID = "heartbeat:device:id";

    /**
     * value前缀
     */
    public static final String KEY_PREFIX_VALUE = "focus:iot:value:";

    /**
     * set前缀
     */
    public static final String KEY_PREFIX_SET = "focus:iot:set:";

    /**
     * list前缀
     */
    public static final String KEY_PREFIX_LIST = "focus:iot:list:";

    /**
     * hash前缀
     */
    public static final String KEY_PREFIX_HASH = "focus:iot:hash:";

    /**
     * 配置key
     */
    public static final String CONFIG_KEY = "config";

    /**
     * token key
     */
    public static final String TOKEN_KEY = "token:";

    /**
     * 通知 key
     */
    public static final String NOTICE_KEY = "topic:notice";

    /**
     * 分
     */
    public static final Long MINUTE_EXPIRE = 60L;

    /**
     * 小时
     */
    public static final Long HOUR_EXPIRE = 60L * 60;

    /**
     * 90 分钟
     */
    public static final Long NINETY_MINUTE_EXPIRE = 60L * 90;

    /**
     * 天
     */
    public static final Long DAY_EXPIRE = 60L * 60 * 24;
}
