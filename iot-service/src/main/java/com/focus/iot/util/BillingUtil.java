package com.focus.iot.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BillingUtil {

    /**
     * 每15秒计费一个单位，不足15秒按1单位算
     *
     * @param seconds 时长（单位：秒）
     * @return 计费单位数量（BigDecimal）
     */
    public static BigDecimal computeBillingUnits(BigDecimal seconds) {
        if (seconds == null || seconds.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal unitSeconds = new BigDecimal("15");
        return seconds.divide(unitSeconds, 0, RoundingMode.UP); // 向上取整
    }

    /**
     * 示例：计算总费用
     *
     * @param seconds   视频或音频时长（秒）
     * @param unitPrice 每单位价格
     * @return 总费用
     */
    public static BigDecimal calculateFee(BigDecimal seconds, BigDecimal unitPrice) {
        return computeBillingUnits(seconds).multiply(unitPrice);
    }
}
