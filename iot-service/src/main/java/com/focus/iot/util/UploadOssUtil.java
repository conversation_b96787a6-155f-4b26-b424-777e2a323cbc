package com.focus.iot.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.focus.iot.dao.domain.DeviceGpuStatusHistory;
import com.focus.iot.dao.domain.DeviceProcessStatusHistory;
import com.focus.iot.dao.domain.DeviceStatusHistory;
import com.focus.iot.dao.mapper.DeviceGpuStatusHistoryMapper;
import com.focus.iot.dao.mapper.DeviceProcessStatusHistoryMapper;
import com.focus.iot.dao.mapper.DeviceStatusHistoryMapper;
import com.focus.iot.dao.service.IConfigService;
import com.focus.iot.enums.UploadFileTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.ibatis.cursor.Cursor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @Date 2025/8/28
 * @Desc
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class UploadOssUtil {
    // OSS配置（从配置文件注入）
    @Value("${oss.file.bucket}")
    private String bucketName;

    @Value("${oss.file.endpoint}")
    private String endpoint;

    @Autowired
    private IConfigService configService;

    @Autowired
    private DeviceGpuStatusHistoryMapper deviceGpuStatusHistoryMapper;

    @Autowired
    private DeviceStatusHistoryMapper deviceStatusHistoryMapper;

    @Autowired
    private DeviceProcessStatusHistoryMapper deviceProcessStatusHistoryMapper;

    // 批次日志打印间隔
    private static final int BATCH_LOG_SIZE = 1000;

    @Transactional(readOnly = true)
    public long uploadDataToOSS(LocalDateTime startTime, LocalDateTime endTime, String fileName, String fileType) {
        OSS ossClient = new OSSClientBuilder().build(
                endpoint,
                configService.getTextConfig("oss", "accessKeyId"),
                configService.getTextConfig("oss", "accessKeySecret")
        );
        AtomicLong totalCount = new AtomicLong(0);
        long uploadStartTime = System.currentTimeMillis();

        // 配置OSS对象元数据（指定文件类型和编码）
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType("text/csv; charset=UTF-8");

        // 获取文件类型处理器
        FileTypeProcessor processor = getFileTypeProcessor(fileType);
        if (processor == null) {
            throw new IllegalArgumentException("不支持的文件类型: " + fileType);
        }

        try (
                // 初始化处理器（获取游标和CSV打印器）
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                CSVPrinter csvPrinter = processor.createCsvPrinter(buffer);
                Cursor<?> cursor = processor.createCursor(startTime, endTime)
        ) {
            // 处理数据并写入CSV
            processData(cursor, csvPrinter, buffer, totalCount);
            // 将CSV打印器刷新到缓冲区中，确保所有数据都被写入
            csvPrinter.flush();
            // 将缓冲区数据上传到OSS
            return uploadToOSS(ossClient, buffer, fileName, metadata, totalCount, uploadStartTime);

        } catch (Exception e) {
            // 发生异常时删除可能的不完整文件
            handleUploadException(ossClient, bucketName, fileName, e);
            return 0;
        }
    }

    /**
     * 根据文件类型获取对应的处理器
     */
    private FileTypeProcessor getFileTypeProcessor(String fileType) {
        if (UploadFileTypeEnum.DEVICE_STATUS_HISTORY.getType().equals(fileType)) {
            return new DeviceStatusFileTypeProcessor();
        } else if (UploadFileTypeEnum.GPU_STATUS_HISTORY.getType().equals(fileType)) {
            return new GpuStatusFileTypeProcessor();
        } else if (UploadFileTypeEnum.PROCESS_STATUS_HISTORY.getType().equals(fileType)) {
            return new ProcessStatusFileTypeProcessor();
        }
        return null;
    }

    /**
     * 处理数据并写入CSV
     */
    private <T> void processData(Cursor<T> cursor, CSVPrinter csvPrinter,
                                 ByteArrayOutputStream buffer, AtomicLong totalCount) throws IOException {
        for (T data : cursor) {
            // 委托给具体处理器处理数据行
            if (data instanceof DeviceStatusHistory) {
                printDeviceStatusRecord(csvPrinter, (DeviceStatusHistory) data);
            } else if (data instanceof DeviceGpuStatusHistory) {
                printGpuStatusRecord(csvPrinter, (DeviceGpuStatusHistory) data);
            } else if (data instanceof DeviceProcessStatusHistory) {
                printProcessStatusRecord(csvPrinter, (DeviceProcessStatusHistory) data);
            }

            totalCount.incrementAndGet();

            // 每BATCH_LOG_SIZE条打印日志
            if (totalCount.get() % BATCH_LOG_SIZE == 0) {
                log.info("已处理 {} 条数据，当前缓冲区大小：{} KB",
                        totalCount.get(), buffer.size() / 1024);
            }
        }
    }

    /**
     * 将数据上传到OSS
     */
    private long uploadToOSS(OSS ossClient, ByteArrayOutputStream buffer, String fileName,
                             ObjectMetadata metadata, AtomicLong totalCount, long uploadStartTime) throws IOException {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(buffer.toByteArray())) {
            PutObjectRequest putRequest = new PutObjectRequest(bucketName, fileName, inputStream, metadata);
            PutObjectResult result = ossClient.putObject(putRequest);

            long costTime = (System.currentTimeMillis() - uploadStartTime) / 1000;
            log.info("===== 全部数据上传完成！总条数：{}，OSS路径：{}，ETag：{}，耗时：{} 秒 =====",
                    totalCount.get(), fileName, result.getETag(), costTime);

            return totalCount.get();
        } finally {
            // 关闭OSS客户端
            ossClient.shutdown();
        }
    }

    /**
     * 处理上传异常
     */
    private void handleUploadException(OSS ossClient, String bucketName, String fileName, Exception e) {
        try {
            if (ossClient.doesObjectExist(bucketName, fileName)) {
                ossClient.deleteObject(bucketName, fileName);
                log.warn("已删除不完整文件：{}", fileName);
            }
        } catch (Exception ex) {
            log.error("删除不完整文件失败", ex);
        } finally {
            ossClient.shutdown();
        }
        log.error("数据上传OSS失败", e);
        throw new RuntimeException("数据上传OSS异常", e);
    }

    /**
     * 打印设备状态记录到CSV
     */
    private void printDeviceStatusRecord(CSVPrinter csvPrinter, DeviceStatusHistory data) throws IOException {
        csvPrinter.printRecord(
                data.getId(),
                data.getDeviceId(),
                data.getCpuUsage(),
                data.getMemoryUsage(),
                data.getDiskUsage(),
                data.getGpuUtilAvg(),
                data.getNetworkIn(),
                data.getNetworkOut(),
                data.getTemperature(),
                data.getHeartbeatTime(),
                data.getCreateTime()
        );
    }

    /**
     * 打印GPU状态记录到CSV
     */
    private void printGpuStatusRecord(CSVPrinter csvPrinter, DeviceGpuStatusHistory data) throws IOException {
        csvPrinter.printRecord(
                data.getId(),
                data.getDeviceId(),
                data.getGpuIndex(),
                data.getUtilization(),
                data.getMemoryUsed(),
                data.getTemperature(),
                data.getPowerDraw(),
                data.getHeartbeatTime(),
                data.getCreateTime()
        );
    }

    /**
     * 打印进程状态记录到CSV
     */
    private void printProcessStatusRecord(CSVPrinter csvPrinter, DeviceProcessStatusHistory data) throws IOException {
        csvPrinter.printRecord(
                data.getId(),
                data.getProcessId(),
                data.getProcessType(),
                data.getDeviceId(),
                data.getGpuIndex(),
                data.getProcessStatus(),
                data.getTaskStatus(),
                data.getTaskId(),
                data.getHeartbeatTime(),
                data.getCreateTime()
        );
    }

    /**
     * 文件类型处理器接口
     */
    private interface FileTypeProcessor {
        CSVPrinter createCsvPrinter(ByteArrayOutputStream buffer) throws IOException;

        Cursor<?> createCursor(LocalDateTime startTime, LocalDateTime endTime);
    }

    /**
     * 设备状态文件处理器
     */
    private class DeviceStatusFileTypeProcessor implements FileTypeProcessor {
        @Override
        public CSVPrinter createCsvPrinter(ByteArrayOutputStream buffer) throws IOException {
            return new CSVPrinter(
                    new OutputStreamWriter(buffer, StandardCharsets.UTF_8),
                    CSVFormat.DEFAULT.withHeader(
                            "id", "device_id", "cpu_usage", "memory_usage",
                            "disk_usage", "gpu_util_avg", "network_in",
                            "network_out", "temperature", "heartbeat_time", "create_time"
                    )
            );
        }

        @Override
        public Cursor<?> createCursor(LocalDateTime startTime, LocalDateTime endTime) {
            return deviceStatusHistoryMapper.selectDataByCursor(startTime, endTime);
        }
    }

    /**
     * GPU状态文件处理器
     */
    private class GpuStatusFileTypeProcessor implements FileTypeProcessor {
        @Override
        public CSVPrinter createCsvPrinter(ByteArrayOutputStream buffer) throws IOException {
            return new CSVPrinter(
                    new OutputStreamWriter(buffer, StandardCharsets.UTF_8),
                    CSVFormat.DEFAULT.withHeader(
                            "id", "device_id", "gpu_index", "utilization",
                            "memory_used", "temperature", "power_draw", "heartbeat_time", "create_time"
                    )
            );
        }

        @Override
        public Cursor<?> createCursor(LocalDateTime startTime, LocalDateTime endTime) {
            return deviceGpuStatusHistoryMapper.selectDataByCursor(startTime, endTime);
        }
    }

    /**
     * 进程状态文件处理器
     */
    private class ProcessStatusFileTypeProcessor implements FileTypeProcessor {
        @Override
        public CSVPrinter createCsvPrinter(ByteArrayOutputStream buffer) throws IOException {
            return new CSVPrinter(
                    new OutputStreamWriter(buffer, StandardCharsets.UTF_8),
                    CSVFormat.DEFAULT.withHeader(
                            "id", "process_id", "process_type", "device_id", "gpu_index", "process_status", "task_status", "task_id", "heartbeat_time", "create_time"
                    )
            );
        }

        @Override
        public Cursor<?> createCursor(LocalDateTime startTime, LocalDateTime endTime) {
            return deviceProcessStatusHistoryMapper.selectDataByCursor(startTime, endTime);
        }
    }

}


