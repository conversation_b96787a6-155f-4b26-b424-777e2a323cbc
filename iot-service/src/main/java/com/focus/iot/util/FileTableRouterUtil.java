package com.focus.iot.util;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class FileTableRouterUtil {
    /**
     * 根据时间范围生成半年表后缀列表
     * @param start
     * @param end
     * @return
     */
    public static List<String> listTableSuffix(LocalDateTime start, LocalDateTime end) {
        List<String> suffixes = new ArrayList<>();
        LocalDateTime cursor = start.withDayOfMonth(1);

        while (!cursor.isAfter(end)) {
            int year = cursor.getYear();
            int month = cursor.getMonthValue();
            String suffix = (month <= 6) ? (year + "06") : (year + "12");

            if (!suffixes.contains(suffix)) {
                suffixes.add(suffix);
            }

            // 跳到下一半年（直接跳到 7 月或明年 1 月）
            if (month <= 6) {
                cursor = LocalDateTime.of(year, 7, 1, 0, 0);
            } else {
                cursor = LocalDateTime.of(year + 1, 1, 1, 0, 0);
            }
        }

        return suffixes;
    }

    /**
     * 根据指定时间获取该时间点应插入的半年表后缀（用于保存）
     * 例：2025-03 ➜ 202506；2025-12 ➜ 202512
     */
    public static String getInsertTableSuffix(LocalDateTime time) {
        int year = time.getYear();
        int month = time.getMonthValue();
        return (month <= 6) ? (year + "06") : (year + "12");
    }

    public static void main(String[] args) {
//        System.out.println(FileTableRouterUtil.listTableSuffix(LocalDateTime.now().minusMonths(1), LocalDateTime.now().plusMonths(24)));
        System.out.println(FileTableRouterUtil.getInsertTableSuffix(LocalDateTime.now()));
    }
}
