package com.focus.iot.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

/**
 * <AUTHOR>
 * @Date 2025/8/27
 * @Desc
 **/
public class LocalDateTimeUtil {
    public static final ZoneId ASIA = ZoneId.of("Asia/Shanghai");

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    public static final String YYYYMMDD = "yyyyMMdd";

    /**
     * 将时间转为指定格式和时区的字符串
     */
    public static LocalDateTime formatGMT8(String originalTime, ZoneId targetZone) {

        // 1. 解析为UTC时区的ZonedDateTime（保留时区信息）
        ZonedDateTime utcTime = ZonedDateTime.parse(originalTime);

        // 2. 转换为目标时区
        ZonedDateTime gmt8Time = utcTime.withZoneSameInstant(targetZone);
        return gmt8Time.toLocalDateTime();
    }

    public static String getYesterdayDate(String format) {
        LocalDateTime now = LocalDateTime.now();
        return now.minusDays(1).format(java.time.format.DateTimeFormatter.ofPattern(format));
    }
}
