package com.focus.iot.util;

import com.focus.iot.dto.IpInfoDTO;
import com.google.gson.Gson;
import jodd.util.StringUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.*;
import java.util.Enumeration;

public class IpUtil {

    public static final Long FIRST_IP_CUT = 16777216L;
    public static final Long SECOND_IP_CUT = 65536L;
    public static final Long THIRD_IP_CUT = 256L;
    public static final Long FOURTH_IP_CUT = 1L;

    public static void main(String[] args) {
        Long num = 16777471L;
        Long w = num/16777216%256;
        Long x = num/65536%256;
        Long y = num/256%256;
        Long z = num%256;

        String ipAddress = String.format("%d.%d.%d.%d",w,x,y,z);
        System.out.println(ipAddress);

        // ************ - **************
//        System.out.println(16777216L *202 + 65536L*186 + 256L*13 + 4L);
//        System.out.println(255*255*255);

        System.out.println(getIpInfo(ipAddress));
    }

    /**
     * 如果IP地址为空，则查询本机IP地址信息
     * */
    public static IpInfoDTO getIpInfo(String ip) {
        // 1、IP地址拼接
        String url = "http://ip-api.com/json/";
        if (!StringUtil.isBlank(ip)) {
            url += ip;
        }

        try {
            // 发送HTTP请求
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);

            // 读取响应
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            // 解析JSON
            Gson gson = new Gson();
            IpInfoDTO ipInfo = gson.fromJson(response.toString(), IpInfoDTO.class);
            // 输出结果
            if ("success".equals(ipInfo.getStatus())) {
                return ipInfo;
            } else {
                return null;
            }
        } catch (IOException e) {
            System.err.println("查询出错: " + e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前主机的内网 IPv4 地址（排除回环、虚拟网卡）
     *
     * @return 内网 IP 地址，如 10.x.x.x / 192.168.x.x / 172.16.x.x，获取失败时返回 null
     */
    public static String getInnerIp() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                if (!ni.isUp() || ni.isLoopback() || ni.isVirtual()) {
                    continue;
                }

                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address instanceof Inet4Address && !address.isLoopbackAddress()) {
                        String ip = address.getHostAddress();
                        // 判断是否为内网 IP（10. / 192.168. / 172.16-31）
                        if (isInnerIP(ip)) {
                            return ip;
                        }
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return null; // 未找到合适的内网 IP
    }

    /**
     * 判断是否为内网 IP
     */
    private static boolean isInnerIP(String ip) {
        if (ip.startsWith("10.") || ip.startsWith("192.168.")) {
            return true;
        }
        if (ip.startsWith("172.")) {
            String[] parts = ip.split("\\.");
            if (parts.length >= 2) {
                try {
                    int second = Integer.parseInt(parts[1]);
                    return second >= 16 && second <= 31;
                } catch (NumberFormatException ignored) {
                }
            }
        }
        return false;
    }
}
