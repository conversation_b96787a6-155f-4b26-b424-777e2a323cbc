package com.focus.iot.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class test {
    public static void main(String[] args) {
        String sk="0f6077f8e4ff4a5b86fae47c";
        AES aes = SecureUtil.aes(sk.getBytes());

        String res = aes.encryptHex("AA20250525200001");
        log.info("密文：{}",res);
        String result = aes.decryptStr(res);
        log.info("解密后：{}",result);



        String a="YjJiNjYxNWNiZTVjMTBkMmNjN2VkZGIxMmEwOWQ3NWFmYzM1NGZlYjFlNjIwMDRkMmI3NWY2NDU5ZDcxZjljOA==";
        String s = Base64.decodeStr(a);
        String r = aes.decryptStr(s);
        log.info("解密后：{}",r);

        String enc = aes.encryptHex("商务版合成视频");
        String encode = Base64.encode(enc);
        log.info("加密后：{}",encode);
//        log.info(StrUtil.toString(apiCheckFileVO));
//
//
//        log.info(JSONUtil.toJsonStr(apiCheckFileVO));
//        String key = "0f6077f8e4ff4a5b86fae47c"; // 16 字节
//        String iv = "0f6077f8e4ff4a5b"; // 16 字节
//
//        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, key.getBytes(), iv.getBytes());
//
//        String content = "Hello, AES!";
//        String encrypted = aes.encryptBase64(content); // Base64 编码输出
//        System.out.println("加密后: " + encrypted);

        Map param = new HashMap();
        param.put("jobId", res);
//        Map<Object, Object> mapParam = new HashMap<>((Map<?, ?>) param);
//
//        for (Map.Entry<Object, Object> entry : mapParam.entrySet()) {
//            Object key = entry.getKey();
//            Object value = entry.getValue();
//
//            if (key instanceof String && value != null) {
//                try {
//                    String encryptVal = Base64.encode(aes.encryptHex(value.toString()));
//                    mapParam.put(key, encryptVal); // ✅ 安全操作
//                } catch (Exception e) {
//                    log.error(e.getMessage());
//                }
//            }
//        }
        Map<String, Object> map = BeanUtil.beanToMap(param);
        log.info(JSONUtil.toJsonStr(map));



    }

}
