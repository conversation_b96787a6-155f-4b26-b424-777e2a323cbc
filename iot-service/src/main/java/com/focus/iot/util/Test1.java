package com.focus.iot.util;

import cn.hutool.http.HttpUtil;

public class Test1 {
    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            HttpUtil.downloadFile("https://p26-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/521ac1582d864481904acc34f510349c.MP3~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1780451851&x-signature=qH%2B2V3u42MW5gzz8XvaShVS6gMM%3D&x-wf-file_name=6%E6%9C%887%E6%97%A5%281%29.MP3", "C:\\Users\\<USER>\\Desktop\\" + i + ".mp3");
            System.out.println(i);
        }

    }


}
