package com.focus.iot.util;

import cn.hutool.core.util.StrUtil;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;

/**
 * 工具类：生成带签名的安全 URL，用于 OpenResty/Nginx 文件保护。
 */
public class SignedUrlGenerator {

    /**
     * 生成带签名的完整访问 URL
     *
     * @param baseUrl 基础域名，例如：http://example.com:91
     * @param path 文件相对路径，例如：/videos/abc.mp4 或 videos/abc.mp4
     * @param secret 与 Nginx 中配置的密钥一致
     * @param ttlSeconds 有效期，单位为秒（从当前时间起）
     * @return 带签名的完整 URL
     * @throws Exception 加密失败时抛出
     */
    public static String generateSignedUrl(String baseUrl, String path, String secret, long ttlSeconds) throws Exception {
        long expires = Instant.now().getEpochSecond() + ttlSeconds;
        String fixedPath = path.startsWith("/") ? path : "/" + path;
        String toSign = fixedPath + "@" + expires;
        String sign = hmacSha256Hex(secret, toSign);
        String encodedSign = URLEncoder.encode(sign, StandardCharsets.UTF_8.name());

        // 使用 StrUtil.format 拼接
        return StrUtil.format("{}{}?e={}&sign={}", baseUrl, fixedPath, expires, encodedSign);
    }

    // HMAC-SHA256 签名
    private static String hmacSha256Hex(String secret, String message) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256_HMAC.init(secretKey);
        byte[] hash = sha256_HMAC.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }

    // 字节数组转十六进制字符串（小写）
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes)
            hexString.append(String.format("%02x", b));
        return hexString.toString();
    }

    // 示例调用
    public static void main(String[] args) throws Exception {
        String secret = "^focus20250704AI^！";
        String baseUrl = "http://120.240.236.183:91";
        String path = "CC2025071400000013/8af564baf3254abfb893f457a7f653b2.mp4";
        long ttlSeconds = 3600;

        String signedUrl = generateSignedUrl(baseUrl, path, secret, ttlSeconds);
        System.out.println("Signed URL:");
        System.out.println(signedUrl);
    }
}
