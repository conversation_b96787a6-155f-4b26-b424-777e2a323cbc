package com.focus.iot.task;

import com.focus.iot.constant.HeartBeatConstant;
import com.focus.iot.dao.service.IConfigService;
import com.focus.iot.dao.service.IDeviceGpuStatusHistoryService;
import com.focus.iot.dao.service.IDeviceProcessStatusHistoryService;
import com.focus.iot.dao.service.IDeviceStatusHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/8/28
 * @Desc
 **/
@Slf4j
@Component
public class UploadDeviceHeartbeatDataTask {
    // 分布式锁名称 - 确保任务在集群环境中仅执行一次
    private static final String TASK_LOCK_KEY = "upload:device:heartbeat:data:task:lock";

    // 锁超时时间 - 防止任务执行异常导致锁无法释放，此处设置为30分钟
    private static final long LOCK_TIMEOUT = 30;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IDeviceGpuStatusHistoryService deviceGpuStatusHistoryService;

    @Autowired
    private IDeviceStatusHistoryService deviceStatusHistoryService;

    @Autowired
    private IDeviceProcessStatusHistoryService deviceProcessStatusHistoryService;

    @Autowired
    private IConfigService iConfigService;

    /**
     * 每天凌晨1点执行，上传当天的设备心跳数据到OSS
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void execute() {
        long taskStartTime = System.currentTimeMillis();
        log.info("===== 上传设备心跳数据任务开始执行 =====");

        // 获取分布式锁
        RLock lock = redissonClient.getLock(TASK_LOCK_KEY);
        boolean isLocked = false;

        try {
            // 尝试获取锁，最多等待0秒，持有锁最多LOCK_TIMEOUT秒
            isLocked = lock.tryLock(0, LOCK_TIMEOUT, TimeUnit.MINUTES);

            if (isLocked) {
                log.info("成功获取分布式锁，开始执行数据清理逻辑");

                // 执行实际的上传操作
                uploadDeviceHeartbeatDataToOSS();

                log.info("上传设备心跳数据任务执行完成");
            } else {
                log.warn("未获取到分布式锁，任务已被其他节点执行，当前节点跳过");
            }
        } catch (InterruptedException e) {
            log.error("任务执行被中断", e);
            Thread.currentThread().interrupt(); // 恢复中断状态
        } catch (Exception e) {
            log.error("上传设备心跳数据任务执行失败", e);
        } finally {
            // 确保锁被正确释放
            if (isLocked && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.info("分布式锁已释放");
                } catch (Exception e) {
                    log.error("释放分布式锁失败", e);
                }
            }
            // 计算并记录任务总执行时间
            long taskEndTime = System.currentTimeMillis();
            long totalDurationMs = taskEndTime - taskStartTime;
            double totalDurationSec = totalDurationMs / 1000.0;
            log.info("===== 上传设备心跳数据任务执行结束，总耗时: {} ms ({} 秒) =====", totalDurationMs, String.format("%.2f", totalDurationSec));
        }
    }

    /**
     * 上传设备心跳信息到OSS
     */
    private void uploadDeviceHeartbeatDataToOSS() {
        try {
            // 1、构造时间
            LocalDateTime startTime = LocalDateTime.now().minusDays(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            String region = iConfigService.getTextConfig(HeartBeatConstant.DEVICE, HeartBeatConstant.REGION);

            // 1、上传当天的GPU设备心跳数据
            long gpuStatusDataNum = deviceGpuStatusHistoryService.uploadGpuStatusDataToOSS(startTime, endTime, region);
            log.info("成功上传{}条GPU设备心跳数据", gpuStatusDataNum);

            // 2、上传当天的设备心跳数据
            long deviceStatusDataNum = deviceStatusHistoryService.uploadDeviceStatusDataToOSS(startTime, endTime, region);
            log.info("成功上传{}条设备心跳数据", deviceStatusDataNum);

            // 3、上传当天进程的心跳数据
            long processStatusDataNum = deviceProcessStatusHistoryService.uploadProcessStatusDataToOSS(startTime, endTime, region);
            log.info("成功上传{}条进程心跳数据", processStatusDataNum);
        } catch (Exception e) {
            log.error("上传设备心跳数据时发生异常", e);
        }
    }
}
