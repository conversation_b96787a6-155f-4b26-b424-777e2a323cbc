package com.focus.iot.task;

import com.focus.iot.dao.service.IDeviceGpuStatusHistoryService;
import com.focus.iot.dao.service.IDeviceProcessStatusHistoryService;
import com.focus.iot.dao.service.IDeviceStatusHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/8/28
 * @Desc 每天凌晨2点执行，清理设备心跳三天前的历史数据
 **/
@Slf4j
@Component
public class ClearDeviceHeartbeatHistoryDataTask {

    // 分布式锁名称 - 确保任务在集群环境中仅执行一次
    private static final String TASK_LOCK_KEY = "clear:device:heartbeat:history:task:lock";

    // 锁超时时间 - 防止任务执行异常导致锁无法释放，此处设置为30分钟
    private static final long LOCK_TIMEOUT = 30;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IDeviceGpuStatusHistoryService deviceGpuStatusHistoryService;

    @Autowired
    private IDeviceStatusHistoryService deviceStatusHistoryService;

    @Autowired
    private IDeviceProcessStatusHistoryService deviceProcessStatusHistoryService;

    /**
     * 每天凌晨2点执行，清理设备心跳三天前的历史数据
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void execute() {
        long taskStartTime = System.currentTimeMillis();
        log.info("===== 设备心跳历史数据清理任务开始执行 =====");

        // 获取分布式锁
        RLock lock = redissonClient.getLock(TASK_LOCK_KEY);
        boolean isLocked = false;

        try {
            // 尝试获取锁，最多等待0秒，持有锁最多LOCK_TIMEOUT秒
            isLocked = lock.tryLock(0, LOCK_TIMEOUT, TimeUnit.MINUTES);

            if (isLocked) {
                log.info("成功获取分布式锁，开始执行数据清理逻辑");

                // 执行实际的清理操作
                clearHistoryData();

                log.info("数据清理逻辑执行完成");
            } else {
                log.warn("未获取到分布式锁，任务已被其他节点执行，当前节点跳过");
            }
        } catch (InterruptedException e) {
            log.error("任务执行被中断", e);
            Thread.currentThread().interrupt(); // 恢复中断状态
        } catch (Exception e) {
            log.error("数据清理任务执行失败", e);
        } finally {
            // 确保锁被正确释放
            if (isLocked && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                    log.info("分布式锁已释放");
                } catch (Exception e) {
                    log.error("释放分布式锁失败", e);
                }
            }
            // 计算并记录任务总执行时间
            long taskEndTime = System.currentTimeMillis();
            long totalDurationMs = taskEndTime - taskStartTime;
            double totalDurationSec = totalDurationMs / 1000.0;
            log.info("===== 设备心跳历史数据清理任务执行结束，总耗时: {} ms ({} 秒) =====",
                    totalDurationMs, String.format("%.2f", totalDurationSec));
        }
    }

    /**
     * 只保留三天的数据，超过三天的数据全部清空
     */
    private void clearHistoryData() {
        try {
            // 1、构造时间
            LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3).withHour(0).withMinute(0).withSecond(0);

            // 2、清理GPU状态历史数据
             long removeGpuHistoryNum = deviceGpuStatusHistoryService.clearGpuStatusHistory(threeDaysAgo);
             log.info("成功清理{}条3天前GPU设备心跳历史数据", removeGpuHistoryNum);

             // 3、清理设备状态历史数据
             long removeDeviceHistoryNum = deviceStatusHistoryService.clearDeviceStatusHistory(threeDaysAgo);
             log.info("成功清理{}条3天前设备心跳历史数据", removeDeviceHistoryNum);

            // 4、清理线程状态历史数据
            long removeProcessHistoryNum = deviceProcessStatusHistoryService.clearProcessStatusHistory(threeDaysAgo);
             log.info("成功清理{}条3天前进程心跳历史数据", removeProcessHistoryNum);

        } catch (Exception e) {
            log.error("执行数据清理逻辑时发生异常", e);
        }
    }
}
