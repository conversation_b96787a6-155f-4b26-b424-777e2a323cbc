package com.focus.iot.task;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.focus.iot.dao.domain.DeviceProcessStatus;
import com.focus.iot.dao.mapper.DeviceProcessStatusMapper;
import com.focus.iot.dto.ProcessPayloadDTO;
import com.focus.iot.dto.ProcessRequestDTO;
import com.focus.iot.enums.ProcessStatusEnum;
import com.focus.iot.enums.WebsocketTypeEnum;
import com.focus.iot.registry.SessionRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2025/9/3
 * @desc 和设备测试定时任务
 **/
@Slf4j
@Component
public class ControlTask {

    @Autowired
    private DeviceProcessStatusMapper deviceProcessStatusMapper;

    @Autowired
    private SessionRegistry sessionRegistry;

    public static final String[] COMMANDS = {"start", "stop", "down", "restart"};

//    @Scheduled(cron = "0 0/5 * * * ?")
    public void execute() throws InterruptedException {
        log.info("===== 随机抽取命令执行开始 =====");
        Random random = new Random();
        for (int i = 0; i <= 7; i++) {
            int command = random.nextInt(4); // 取值 0-3
            log.info("\n顺序执行命令：1start,2stop,3dwon,4restart, 现执行：{}", command+1);
            Thread.sleep(15000);
            exeCommand(i, command);
        }

    }

    public void exeCommand(Integer command, Integer number) {
        DeviceProcessStatus deviceProcessStatus = deviceProcessStatusMapper.selectOne(
                new QueryWrapper<DeviceProcessStatus>()
                        .eq("gpu_index", number)
        );
        if (deviceProcessStatus == null) {
            log.info("未找到符合条件的进程，跳过执行");
            return;
        }

        int processStatus = deviceProcessStatus.getProcessStatus();

        // 2、校验命令
        if (command == 1) {
            boolean isStopped = ProcessStatusEnum.STOPPED.getCode().equals(processStatus);
            boolean isShutdown = ProcessStatusEnum.SHUTDOWN.getCode().equals(processStatus);
            if (isStopped || isShutdown) {
                log.info("执行启动命令");
                sendCommand(deviceProcessStatus, command, number);
                return;
            }
            log.info("当前进程状态：{}，无需执行启动命令", ProcessStatusEnum.getByCode(processStatus).getDesc());
        } else if (command == 2) {
            boolean isRunning = ProcessStatusEnum.RUNNING.getCode().equals(processStatus);
            boolean isStarting = ProcessStatusEnum.STARTING.getCode().equals(processStatus);
            if (isRunning || isStarting) {
                log.info("执行暂停命令");
                sendCommand(deviceProcessStatus, command, number);
                return;
            }
            log.info("当前进程状态：{}，无需执行停止命令", ProcessStatusEnum.getByCode(processStatus).getDesc());
        } else if (command == 3) {
            boolean isShutdown = ProcessStatusEnum.SHUTDOWN.getCode().equals(processStatus);
            if (!isShutdown) {
                log.info("执行关闭命令");
                sendCommand(deviceProcessStatus, command, number);
                return;
            }
            log.info("当前进程状态：{}，无需执行关闭命令", ProcessStatusEnum.getByCode(processStatus).getDesc());
        } else {
            boolean isStarting = ProcessStatusEnum.STARTING.getCode().equals(processStatus);
            if (!isStarting) {
                log.info("执行重启命令");
                sendCommand(deviceProcessStatus, command, number);
                return;
            }
            log.info("当前进程状态：{}，无需执行重启命令", ProcessStatusEnum.getByCode(processStatus).getDesc());
        }
    }

    // 3、发送命令
    public void sendCommand(DeviceProcessStatus deviceProcessStatus, Integer command, Integer number) {
        String deviceId = deviceProcessStatus.getDeviceId();
        ProcessRequestDTO requestDTO = ProcessRequestDTO.builder()
                .type(WebsocketTypeEnum.CONTROL.getName())
                .payload(ProcessPayloadDTO.builder()
                        .request_id("request_id")
                        .gpu_index(number)
                        .process_id(deviceProcessStatus.getProcessId())
                        .action(COMMANDS[command])
                        .build())
                .build();
        WebSocketSession wsSession = sessionRegistry.getDeviceSession(deviceId);
        try {
            if (wsSession != null) {
                log.info("向设备发送操作命令: {}", requestDTO);
                wsSession.sendMessage(new TextMessage(JSONUtil.toJsonStr(requestDTO)));
            }else{
                log.info("session不存在，跳过发送命令");
            }
            log.info("===== 随机抽取命令执行结束 =====");
        } catch (IOException e) {
            log.error("发送设备操作命令消息失败: {}", e.getMessage());
        }
    }

}
