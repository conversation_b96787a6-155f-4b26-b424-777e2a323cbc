package com.focus.iot.task;

import com.focus.iot.dao.mapper.DeviceProcessStatusMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Random;

/**
 * <AUTHOR>
 * @date 2025/9/3
 * @desc
 **/
@Slf4j
@Component
public class ControlTask {

    @Autowired
    private DeviceProcessStatusMapper deviceProcessStatusMapper;

    @Scheduled(cron = "0/30 * * * * ?")
    public void execute(){
        log.info("===== 随机抽取命令执行开始 =====");
        // 1、抽取命令
        Random random = new Random();
        int number = random.nextInt(4) + 1;
        log.info("随机获取命令：1start,2stop,3kill,4restart, 现执行：{}",number);

        deviceProcessStatusMapper.selectOne();

        // 2、校验命令
        if (number == 1) {
            log.info("执行启动命令");
        } else if (number == 2) {
            log.info("执行停止命令");
        } else if (number == 3) {
            log.info("执行关闭命令");
        } else if (number == 4) {
            log.info("执行重启命令");
        }

        // 3、发送命令



    }
}
