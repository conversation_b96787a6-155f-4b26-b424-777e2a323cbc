package com.focus.iot.notice;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.taobao.api.ApiException;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;

public class CustomRobotMessageSender {

    public static final String CUSTOM_ROBOT_TOKEN = "60b8387957931a9239f5e69798181fb625d21e4e9b7da5d15cac27b252baca7f";
    public static final String SECRET = "SECdd55f8a1b06b778e14a11e282ca11e127574c58975182f18bdf716a3f7716460";

    public static void sendMessage(String messageContent, String userId) {
        try {
            // 获取当前时间戳
            Long timestamp = System.currentTimeMillis();
            String secret = SECRET;
            String stringToSign = timestamp + "\n" + secret;

            // 生成签名
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");

            // 创建请求客户端
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?sign=" + sign + "&timestamp=" + timestamp);
            OapiRobotSendRequest req = new OapiRobotSendRequest();

            // 发送文本消息
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(messageContent);

            // 设置 @ 对象
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtUserIds(Arrays.asList(userId));

            // 设置消息类型
            req.setMsgtype("text");
            req.setText(text);
            req.setAt(at);

            // 发送请求并输出结果
            OapiRobotSendResponse rsp = client.execute(req, CUSTOM_ROBOT_TOKEN);
            System.out.println(rsp.getBody());

        } catch (ApiException e) {
            e.printStackTrace();
        } catch (UnsupportedEncodingException | NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        // 调用方法发送消息
        String messageContent = "钉钉，让进步发生";
        String userId = "<you need @ group user's userId>"; // 替换为实际用户 ID
        sendMessage(messageContent, userId);
    }
}
