package com.focus.iot.registry;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class SessionRegistry {

    private final Map<String, WebSocketSession> deviceSessions = new ConcurrentHashMap<>();
    private final Map<WebSocketSession, String> sessionToDeviceId = new ConcurrentHashMap<>();
    private final Map<String, WebSocketSession> processSessions = new ConcurrentHashMap<>();
    private final Map<WebSocketSession, String> sessionToProcessId = new ConcurrentHashMap<>();
    private final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    private final Map<WebSocketSession, String> sessionToUserId = new ConcurrentHashMap<>();

    public void addDevice(String deviceId, WebSocketSession session) {
        deviceSessions.put(deviceId, session);
        sessionToDeviceId.put(session, deviceId);
    }

    public void addProcess(String processId, WebSocketSession session){
        processSessions.put(processId, session);
        sessionToProcessId.put(session, processId);
    }

    public void addUser(String userId, WebSocketSession session) {
        userSessions.put(userId, session);
        sessionToUserId.put(session, userId);
    }

    public WebSocketSession getDeviceSession(String deviceId) {
        return deviceSessions.get(deviceId);
    }

    public WebSocketSession getUserSession(String userId) {
        return userSessions.get(userId);
    }

    public String getDeviceId(WebSocketSession session) {
        return sessionToDeviceId.get(session);
    }

    public String getUserId(WebSocketSession session) {
        return sessionToUserId.get(session);
    }

    public boolean isAuthenticated(WebSocketSession session) {
        return sessionToDeviceId.containsKey(session) || sessionToProcessId.containsKey(session) || sessionToUserId.containsKey(session);
    }

    public Map<String, WebSocketSession> getDeviceSessions() {
        return deviceSessions;
    }

    public Map<String, WebSocketSession> getUserSessions() {
        return userSessions;
    }

    public Map<WebSocketSession, String> getSessionToDeviceId() {
        return sessionToDeviceId;
    }

    public Map<WebSocketSession, String> getSessionToUserId() {
        return sessionToUserId;
    }
}
